AWSTemplateFormatVersion: 2010-09-09
Description: Axonius Federal GOV SAAS Regional Resources Stack v0.0.1

Parameters:
  SaasParamKeyName:
    Type: String
    Default: "saas-params-kms"

  Environment:
    Type: String
    Description: The environment type
    AllowedValues:
      - gov-dev
      - gov-prod
    Default: gov-prod

  # SaasAlertTopicArn:
  #   Type: String
  #   Default: arn:aws:sns:us-east-1:************:SaaSAlerts

Mappings:
  RegionsToElasticLoadBalancingAccountID:
      ## https://docs.aws.amazon.com/elasticloadbalancing/latest/classic/enable-access-logs.html
      us-east-1:
        elbAccountId:  arn:aws:iam::************:root
      us-east-2:
        elbAccountId:  arn:aws:iam::************:root
      us-west-1:
        elbAccountId:  arn:aws:iam::************:root
      us-west-2:
        elbAccountId:  arn:aws:iam::************:root
      af-south-1:
        elbAccountId:  arn:aws:iam::************:root
      ca-central-1:
        elbAccountId:  arn:aws:iam::************:root
      eu-central-1:
        elbAccountId:  arn:aws:iam::************:root
      eu-west-1:
        elbAccountId:  arn:aws:iam::************:root
      eu-west-2:
        elbAccountId:  arn:aws:iam::************:root
      eu-south-1:
        elbAccountId:  arn:aws:iam::************:root
      eu-west-3:
        elbAccountId: arn:aws:iam::************:root
      eu-north-1:
        elbAccountId:  arn:aws:iam::************:root
      ap-east-1:
        elbAccountId:  arn:aws:iam::************:root
      ap-northeast-1:
        elbAccountId:  arn:aws:iam::************:root
      ap-northeast-2:
        elbAccountId:  arn:aws:iam::************:root
      ap-northeast-3:
        elbAccountId:  arn:aws:iam::************:root
      ap-southeast-1:
        elbAccountId:  arn:aws:iam::************:root
      ap-southeast-2:
        elbAccountId:  arn:aws:iam::************:root
      ap-south-1:
        elbAccountId:  arn:aws:iam::************:root
      me-south-1:
        elbAccountId:  arn:aws:iam::************:root
      sa-east-1:
        elbAccountId:  arn:aws:iam::************:root
      us-gov-east-1:
        elbAccountId:  arn:aws-us-gov:iam::************:root
      us-gov-west-1:
        elbAccountId:  arn:aws-us-gov:iam::************:root


Conditions:
  ProdAccount:
    Fn::And:
      - Fn::Equals:
          - Ref: AWS::AccountId
          - "************"
      - Fn::Equals:
          - Ref: Environment
          - "gov-prod"

Resources:
  ## disabled as gov cloud does not support r53
  # OnAxoniusCertificate:
  #   Type: AWS::CertificateManager::Certificate
  #   Properties:
  #     DomainName: !FindInMap [ EnvironmentConfig, !Ref Environment, DomainName ]
  #     ValidationMethod: "DNS"
  #     Tags:
  #       - Key: CreatedBy
  #         Value: "cloudformation"
  #       - Key: Environment
  #         Value: !Ref Environment

  SaasParamKms:
    Type: AWS::KMS::Key
    Properties:
      Description: !Sub "${SaasParamKeyName}-${Environment}"
      Tags:
        - Key: CreatedBy
          Value: "cloudformation"
        - Key: Environment
          Value: !Ref Environment
      EnableKeyRotation: true
      KeyPolicy:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              "AWS": { "Fn::Sub": "arn:${AWS::Partition}:iam::${AWS::AccountId}:root" }
            Action:
              - kms:*
            Resource: "*"

  SaasParamKmsAlias:
    Type: AWS::KMS::Alias
    Properties:
      AliasName: !Sub "alias/${SaasParamKeyName}-${Environment}"
      TargetKeyId: !Ref SaasParamKms


  # Backup ec2 resources
  SaasBackupKMSKey:
    Type: AWS::KMS::Key
    Properties:
      Description: "Encryption key for backups"
      EnableKeyRotation: True
      KeyPolicy:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              "AWS": { "Fn::Sub": "arn:${AWS::Partition}:iam::${AWS::AccountId}:root" }
            Action:
              - kms:*
            Resource: "*"

  SaasBackupKmsAlias:
    Type: AWS::KMS::Alias
    Properties:
      AliasName: !Sub "alias/SaasBackupKMSKey-${Environment}"
      TargetKeyId: !Ref SaasBackupKMSKey

  SaasBackupVaultWithDailyBackups:
    Type: "AWS::Backup::BackupVault"
    Properties:
      BackupVaultName: "SaasBackupVaultWithDailyBackups"
      EncryptionKeyArn: !GetAtt SaasBackupKMSKey.Arn

  SaasBackupPlanWithThinBackups:
    Type: "AWS::Backup::BackupPlan"
    Properties:
      BackupPlan:
        BackupPlanName: "Backup_axonius_saas_ec2_instances"
        BackupPlanRule:
          - RuleName: "RuleForDailyBackups"
            TargetBackupVault: !Ref SaasBackupVaultWithDailyBackups
            ScheduleExpression: "cron(0 5 ? * * *)"
            Lifecycle:
              DeleteAfterDays: 7

  SaasBackupRole:
    Type: "AWS::IAM::Role"
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: "Allow"
            Principal:
              Service:
                - "backup.amazonaws.com"
            Action:
              - "sts:AssumeRole"
      ManagedPolicyArns:
        - "arn:aws-us-gov:iam::aws:policy/service-role/AWSBackupServiceRolePolicyForBackup"

  SaasTagBasedBackupSelection:
    Type: "AWS::Backup::BackupSelection"
    Properties:
      BackupSelection:
        SelectionName: "AxoniusSaasMasters"
        IamRoleArn: !GetAtt SaasBackupRole.Arn
        ListOfTags:
          - ConditionType: "STRINGEQUALS"
            ConditionKey: "aws:cloudformation:logical-id"
            ConditionValue: "MasterEc2Instance"
      BackupPlanId: !Ref SaasBackupPlanWithThinBackups
    DependsOn: SaasBackupPlanWithThinBackups

  SsmCommandRegisterCustomer:
    Type: AWS::SSM::Document
    Properties:
      Content:
        schemaVersion: '2.2'
        description: 'Runs the register customer script'
        parameters:
          companyname:
            type: String
            default: ''
            allowedPattern: "[ 0-9a-zA-Z-_.@:/]*"
          email:
            type: String
            default: ''
            allowedPattern: "[0-9a-zA-Z-_.@:/]*"
          weburl:
            type: String
            default: ''
            allowedPattern: "[0-9a-zA-Z-_.@:/]*"
          originalpurpose:
            type: String
            default: ''
            allowedPattern: "[0-9a-zA-Z-_.@:/]*"
        mainSteps:
          - action: aws:runShellScript
            name: runCommands
            inputs:
              timeoutSeconds: '60'
              runCommand:
                - "/etc/axonius/register_saas.sh --company-name '{{ companyname }}' --customer-email '{{ email }}' --web-url '{{ weburl }}' --original-purpose '{{ originalpurpose }}'"
      DocumentType: Command
      DocumentFormat: "YAML"
      TargetType: "/AWS::EC2::Instance"
      Tags:
        - Key: "Name"
          Value: "RegisterCustomer"

  S3ServerAccessLoggingBucketPolicy:
    DependsOn: S3ServerAccessLoggingBucket
    Type: AWS::S3::BucketPolicy
    UpdateReplacePolicy: Retain
    Properties:
      Bucket: !Ref S3ServerAccessLoggingBucket
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          Effect: Allow
          Principal:
            Service: logging.s3.amazonaws.com
          Action:
            - 's3:PutObject'
          Resource: !Sub 'arn:aws-us-gov:s3:::${S3ServerAccessLoggingBucket}/AWSLogs/*'
          Condition:
            ArnLike:
              'aws:SourceArn': 'arn:aws-us-gov:s3:::*'
            StringEquals:
              'aws:SourceAccount': !Ref "AWS::AccountId"

  ELBAccessLogBucketPolicy:
    DependsOn: ELBAccessLogBucket
    Type: AWS::S3::BucketPolicy
    UpdateReplacePolicy: Retain
    Properties:
      Bucket: !Sub s3-elb-access-logs-${AWS::Region}-${AWS::AccountId}
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              AWS: !FindInMap [ RegionsToElasticLoadBalancingAccountID, !Ref "AWS::Region", elbAccountId ]
            Action: 's3:PutObject'
            Resource:
              Fn::Sub: 'arn:aws-us-gov:s3:::s3-elb-access-logs-${AWS::Region}-${AWS::AccountId}/AWSLogs/${AWS::AccountId}/*'
          - Effect: Allow
            Principal:
              Service: delivery.logs.amazonaws.com
            Action: 's3:PutObject'
            Resource:
              Fn::Sub: 'arn:aws-us-gov:s3:::s3-elb-access-logs-${AWS::Region}-${AWS::AccountId}/AWSLogs/${AWS::AccountId}/*'
            Condition:
              StringEquals:
                's3:x-amz-acl': bucket-owner-full-control
          - Effect: Allow
            Principal:
              Service: delivery.logs.amazonaws.com
            Action: 's3:GetBucketAcl'
            Resource:
              Fn::Sub: 'arn:aws-us-gov:s3:::s3-elb-access-logs-${AWS::Region}-${AWS::AccountId}'

  S3ServerAccessLoggingBucket:
    Type: "AWS::S3::Bucket"
    DeletionPolicy: Retain
    Properties:
      BucketName: !Sub 's3-server-access-logging-${AWS::Region}-${AWS::AccountId}'
      PublicAccessBlockConfiguration:
        BlockPublicAcls: True
        BlockPublicPolicy: True
        IgnorePublicAcls: True
        RestrictPublicBuckets: True
      LifecycleConfiguration:
        Rules:
          - Id: log_expriration
            Status: Enabled
            ExpirationInDays: 30

  ELBAccessLogBucket:
    DependsOn: [S3ServerAccessLoggingBucket, S3ServerAccessLoggingBucketPolicy]
    Type: "AWS::S3::Bucket"
    DeletionPolicy: Retain
    Properties:
      BucketName: !Sub s3-elb-access-logs-${AWS::Region}-${AWS::AccountId}
      LoggingConfiguration:
         DestinationBucketName: !Ref S3ServerAccessLoggingBucket
      PublicAccessBlockConfiguration:
        BlockPublicAcls: True
        BlockPublicPolicy: True
        IgnorePublicAcls: True
        RestrictPublicBuckets: True
      LifecycleConfiguration:
        Rules:
          - Id: log_expriration
            Status: Enabled
            ExpirationInDays: 30

  WAFRegexPatternSet:
    Type: AWS::WAFv2::RegexPatternSet
    Properties:
      Description: Common URIs we wish to exclude from WAF for specific rules
      Name: CommonExcludedURIs
      RegularExpressionList:
        - \/api\/adapters\/.*\/connections\/.*\/v2$
        - \/api\/adapters\/.*\/connections\/test$
        - \/api\/adapters\/.*\/connections$
        - \/api\/queries\/.*
        - \/api\/devices$
        - \/api\/devices\/count$
        - \/api\/users\/count$
        - \/api\/users$
        - \/api\/adapters\/.*\/.*\/upload_file$
      Scope: REGIONAL
      Tags:
        - Key: CreatedBy
          Value: "cloudformation"
        - Key: Environment
          Value: !Ref Environment

  EnforcementWAFWebAcl:
    Type: AWS::WAFv2::WebACL
    Properties:
      DefaultAction:
        Allow: { }
      Description: This is a WAF WebAcl that should be used for selected customers
      Name: !Sub ${AWS::Region}-WebAcl-Enforcement
      Scope: REGIONAL
      Rules:
        - Name: AmazonIpReputationList
          Priority: 0
          OverrideAction:
            None: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: EnforcementAmazonIpReputationListMetric
          Statement:
            ManagedRuleGroupStatement:
              VendorName: AWS
              Name: AWSManagedRulesAmazonIpReputationList
              ExcludedRules: [ ]
        - Name: AnonymousIpList
          Priority: 1
          OverrideAction:
            None: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: EnforcementAnonymousIpListMetric
          Statement:
            ManagedRuleGroupStatement:
              VendorName: AWS
              Name: AWSManagedRulesAnonymousIpList
              RuleActionOverrides:
                # Requested not to inspect as it might block IP coming from hosting providers
                - ActionToUse:
                    Count: { }
                  Name: HostingProviderIPList
              ExcludedRules: [ ]
        - Name: RateLimit
          Priority: 2
          Action:
            Count: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: EnforcementRateLimitMetric
          Statement:
            RateBasedStatement:
              Limit: 5000
              AggregateKeyType: IP
        - Name: CommonRuleSet
          Priority: 3
          OverrideAction:
            None: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: EnforcementCommonRuleSetMetric
          Statement:
            ManagedRuleGroupStatement:
              VendorName: AWS
              Name: AWSManagedRulesCommonRuleSet
              # in gov cloud version might be different, use aws wafv2 list-available-managed-rule-group-versions --scope REGIONAL --vendor-name AWS --name AWSManagedRulesCommonRuleSet
              Version: Version_1.3
              RuleActionOverrides:
                # Requested not to inspect as it might block machine <> machine traffic
                - ActionToUse:
                    Count: { }
                  Name: NoUserAgent_HEADER
                # Count should not be modified as might impact legit traffic
                - ActionToUse:
                    Count: { }
                  Name: SizeRestrictions_BODY
                # Count should not be modified as might impact legit traffic
                - ActionToUse:
                    Count: { }
                  Name: SizeRestrictions_QUERYSTRING
                # Count action should not be changed as we filter and block by AX_Block_EC2MetaDataSSRF_Body rule
                - ActionToUse:
                    Count: { }
                  Name: EC2MetaDataSSRF_BODY
                # Count action should not be changed as we filter and block by AX_Block_GenericRFI_Body rule
                - ActionToUse:
                    Count: { }
                  Name: GenericRFI_BODY
                # Count action should not be changed as we filter and block by AX_Block_EC2MetaDataSSRF_QueryArguments rule
                - ActionToUse:
                    Count: { }
                  Name: EC2MetaDataSSRF_QUERYARGUMENTS
              ExcludedRules: [ ]
        - Name: KnownBadInputsRuleSet
          Priority: 4
          OverrideAction:
            None: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: EnforcementKnownBadInputsRuleSetMetric
          Statement:
            ManagedRuleGroupStatement:
              VendorName: AWS
              Name: AWSManagedRulesKnownBadInputsRuleSet
              Version: Version_1.14
              RuleActionOverrides:
                - ActionToUse: # Requested not to inspect
                    Count: { }
                  Name: JavaDeserializationRCE_BODY
                - ActionToUse: # Requested not to inspect
                    Count: { }
                  Name: JavaDeserializationRCE_URIPATH
                - ActionToUse: # Requested not to inspect
                    Count: { }
                  Name: JavaDeserializationRCE_QUERYSTRING
                - ActionToUse: # Requested not to inspect
                    Count: { }
                  Name: JavaDeserializationRCE_HEADER
                - ActionToUse: # Requested not to inspect
                    Count: { }
                  Name: Log4JRCE_QUERYSTRING
                - ActionToUse: # Requested not to inspect
                    Count: { }
                  Name: Log4JRCE_BODY
                - ActionToUse: # Requested not to inspect
                    Count: { }
                  Name: Log4JRCE_HEADER
                - ActionToUse: # Requested not to inspect
                    Count: { }
                  Name: Log4JRCE_URIPATH
              ExcludedRules: [ ]
        - Name: RulesLinuxRuleSet
          Priority: 5
          OverrideAction:
            None: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: EnforcementRulesLinuxRuleSetMetric
          Statement:
            ManagedRuleGroupStatement:
              VendorName: AWS
              Name: AWSManagedRulesLinuxRuleSet
              Version: Version_2.1
              ExcludedRules: [ ]
        - Name: AX_Block_GenericRFI_Body
          Priority: 6
          Action:
            Block: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: AXBlockGenericRFIBody
          Statement:
            AndStatement:
              Statements:
                - LabelMatchStatement:
                    Key: awswaf:managed:aws:core-rule-set:GenericRFI_Body
                    Scope: LABEL
                - NotStatement:
                    Statement:
                      RegexPatternSetReferenceStatement:
                        Arn: !GetAtt WAFRegexPatternSet.Arn
                        FieldToMatch:
                          UriPath: {}
                        TextTransformations:
                        - Priority: 0
                          Type: NONE
        - Name: AX_Block_EC2MetaDataSSRF_Body
          Priority: 7
          Action:
            Block: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: AXBlockEC2MetaDataSSRFBody
          Statement:
            AndStatement:
              Statements:
                - LabelMatchStatement:
                    Key: awswaf:managed:aws:core-rule-set:EC2MetaDataSSRF_Body
                    Scope: LABEL
                - NotStatement:
                    Statement:
                      RegexPatternSetReferenceStatement:
                        Arn: !GetAtt WAFRegexPatternSet.Arn
                        FieldToMatch:
                          UriPath: {}
                        TextTransformations:
                        - Priority: 0
                          Type: NONE
        - Name: AX_Block_EC2MetaDataSSRF_QueryArguments
          Priority: 8
          Action:
            Block: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: AXBlockEC2MetaDataSSRFQueryArguments
          Statement:
            AndStatement:
              Statements:
                - LabelMatchStatement:
                    Key: awswaf:managed:aws:core-rule-set:EC2MetaDataSSRF_QueryArguments
                    Scope: LABEL
                - NotStatement:
                    Statement:
                      RegexPatternSetReferenceStatement:
                        Arn: !GetAtt WAFRegexPatternSet.Arn
                        FieldToMatch:
                          UriPath: {}
                        TextTransformations:
                        - Priority: 0
                          Type: NONE
      Tags:
        - Key: CreatedBy
          Value: "cloudformation"
        - Key: Environment
          Value: !Ref Environment
      VisibilityConfig:
        SampledRequestsEnabled: true
        CloudWatchMetricsEnabled: true
        MetricName: !Sub ${AWS::Region}-WebAclMetric-Enforcement

  EnforcementWAFWebAclLoggingConfiguration:
    Type: AWS::WAFv2::LoggingConfiguration
    Properties:
      LogDestinationConfigs:
        - !If [ ProdAccount, "arn:aws-us-gov:s3:::aws-waf-logs-axonius-wftdeado-stage", "arn:aws-us-gov:s3:::aws-waf-logs-axonius-aoqntxza-stage" ]
      LoggingFilter:
        DefaultBehavior: DROP
        Filters:
          - Behavior: KEEP
            Conditions:
              - ActionCondition:
                  Action: COUNT
            Requirement: MEETS_ALL
          - Behavior: KEEP
            Conditions:
              - ActionCondition:
                  Action: BLOCK
            Requirement: MEETS_ALL
      RedactedFields:
        - SingleHeader:
            Name: api-secret
      ResourceArn: !GetAtt EnforcementWAFWebAcl.Arn

Outputs:
  # OnAxoniusCertificateArn:
  #   Description: "The On Axonius certificate ARN"
  #   Value: !Ref OnAxoniusCertificate
  #   Export:
  #     Name: !Sub "${AWS::StackName}-OnAxoniusCertificateArn"

  SaasParamKmsArn:
    Description: "The SaaS param KMS ARN"
    Value: !GetAtt SaasParamKms.Arn
    Export:
      Name: !Sub "${AWS::StackName}-SaasParamKmsArn"

  SsmCommandRegisterCustomer: # DO NOT MODIFY THE OUTPUT NAME. It is read in other places and in code
    Description: "SSM command name"
    Value: !Ref SsmCommandRegisterCustomer
    Export:
      Name: !Sub "${AWS::StackName}-SsmCommandRegisterCustomer"

  S3AccessLogArn:
    Description: "The ELB Access log S3 Bucket Name"
    Value: !Ref ELBAccessLogBucket
    Export:
      Name: !Sub "${AWS::StackName}-S3AccessLogsBucketName"

  S3ServerAccessLogArn:
    Description: "S3 server access logging target bucket"
    Value: !Ref S3ServerAccessLoggingBucket
    Export:
      Name: !Sub "${AWS::StackName}-S3ServerAccessLoggingBucketName"

  EnforcementWAFWebAclArn:
    Description: "Enforcement WAF WebAcl ARN"
    Value: !GetAtt EnforcementWAFWebAcl.Arn
    Export:
      Name: !Sub "${AWS::StackName}-WAFWebAclArn-Enforcement"
