<h3 align="center">lab saas-deploy</h3>

---

<p align="center"> Used to deploy new saas environment and customers
    <br> 
</p>

## Deploy New Infrastructure <a name = "getting_started"></a>

To deploy to a new aws region:
1. connect to cloud shell in the relevant aws region
2. run: git clone https://github.com/aws-cloudformation/aws-cloudformation-resource-providers-awsutilities-commandrunner.git
3. cd aws-cloudformation-resource-providers-awsutilities-commandrunner
4. curl -LO https://github.com/aws-cloudformation/aws-cloudformation-resource-providers-awsutilities-commandrunner/releases/latest/download/awsutility-cloudformation-commandrunner.zip
5. sed -i 's/region=`aws configure get region`/region="us-gov-west-1"/g' scripts/register.sh
6. sed -i 's/arn:aws/arn:aws-us-gov/g' scripts/register.sh
7. ./scripts/register.sh --set-default
8. deploy the aws cf: Gov-axonius_waf_instance_template.yaml 
   
## Deploy New Customer

1. deploy the aws cf: Gov-axonius_waf_instance_template.yaml