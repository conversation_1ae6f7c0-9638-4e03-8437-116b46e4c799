s3InstallersBucket="afs-deployment-scripts" # Name of S3 Bucket with installers
linuxDownloadDir="/tmp" # Local directory to download files
awsRegion="us-gov-east-1" # AWS Region name
rpmS3Key="NessusAgent-10.9.0-el8.x86_64.rpm" # S3 Object Key for Splunk UF Agent rpm

aws s3 cp s3://$s3InstallersBucket/$rpmS3Key $linuxDownloadDir/$rpmS3Key --region $awsRegion --no-progress
if [ $? -ne 0 ]; then
  echo "***Failed: Could not download Splunk UF Agent rpm file from S3" && exit 1
fi

echo "Installing Nessus Agent"
sudo yum install -y $linuxDownloadDir/$rpmS3Key --nogpgcheck
if [ $? -ne 0 ]; then
  echo "***Failed: Cannot install" && exit 1
fi

/bin/systemctl start nessusagent.service 
/bin/systemctl enable nessusagent.service 
/opt/nessus_agent/sbin/nessuscli agent link --key=8000894bcdaf50e98263f7f2bc76c7889d9523ae182b70dcdc76035a8011646b --groups=afs-threatalert-prod-rhel8 --host=afsgsstenable01.axonius-threatalert.local --port=8834
/bin/systemctl restart nessusagent.service