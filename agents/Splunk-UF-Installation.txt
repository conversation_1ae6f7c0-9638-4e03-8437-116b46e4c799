# Instructions
# upload installer $rpmS3Key to an existing S3 bucket $s3InstallersBucket
# upload GPG Key $gpgKeyS3Key to an existing S3 Bucket $s3InstallersBucket
# create SSM Secure String parameter with name entered in variable $adminPassParam 
# and place Splunk UF admin password in parameter. Customer can choose UF admin password.
# Update variables in this script with appropriate values

# Run the script on RHEL/AL2 instance

# Requirements
# aws cli installed on instance
# S3 bucket to stage installers and gpg keys
# instance profile will need read access to S3 bucket $s3InstallersBucket
# instance profile will nead read and decrypt access to SSM Parameter $adminPassParam


#!/bin/bash

### Do not change the values of these variables. stackArmor has entered these values for you ###
splunkDsServerName="afsgsssplsh01.axonius-threatalert.local" # Name of Splunk Deployment Server
splunkDsServerPort="8089" # Splunk Deployment Server port


### Update the variables below with appropriate values ###
s3InstallersBucket="afs-deployment-scripts" # Name of S3 Bucket with installers
gpgKeyS3Key="SplunkPGPKey.pub" # S3 Object Key for Splunk GPG Key
linuxDownloadDir="/tmp" # Local directory to download files
awsRegion="us-gov-east-1" # AWS Region name
rpmS3Key="splunkforwarder-9.4.2-e9664af3d956.x86_64.rpm" # S3 Object Key for Splunk UF Agent rpm
adminPassParam="/atom/threatalert-gss/splunk/uf/admin-password" # name of AWS SSM Secure String Parameter with Splunk Admin Password.


# Script block

echo "## Downloading Splunk Key from S3 ##"
aws s3 cp s3://$s3InstallersBucket/$gpgKeyS3Key $linuxDownloadDir/$gpgKeyS3Key --region $awsRegion --no-progress
if [ $? -ne 0 ]; then
  echo "***Failed: Could not download GPG Key file from S3" && exit 1
fi

echo "Importing GPG Key"
rpm --import $linuxDownloadDir/$gpgKeyS3Key
if [ $? -ne 0 ]; then
echo "***Failed: Cannot Import GPG Key" && exit 1
fi

echo "Validating that key is installed"
gpg --with-fingerprint $linuxDownloadDir/$gpgKeyS3Key

echo "Downloading Splunk UF Agent installer from installers bucket"
aws s3 cp s3://$s3InstallersBucket/$rpmS3Key $linuxDownloadDir/$rpmS3Key --region $awsRegion --no-progress
if [ $? -ne 0 ]; then
  echo "***Failed: Could not download Splunk UF Agent rpm file from S3" && exit 1
fi

echo "Validating signature of rpm"
rpm -K $linuxDownloadDir/$rpmS3Key
if [ $? -ne 0 ]; then
  echo "***Failed: Cannot Validate Signature of RPM" && exit 1
fi

echo "Installing Splunk UF Agent"
sudo yum install -y $linuxDownloadDir/$rpmS3Key
if [ $? -ne 0 ]; then
  echo "***Failed: Cannot install Splunk UF Agent" && exit 1
fi

echo "setting Splunk UF FIPS Mode"
cat <<EOT >> /opt/splunkforwarder/etc/splunk-launch.conf

# Enable SPLUNK FIPS Mode
SPLUNK_FIPS=1
EOT

echo "showing contents of splunk-launch.conf. FIPS mode should be enabled here before first start of the UF"
cat /opt/splunkforwarder/etc/splunk-launch.conf

echo "retrieving SSM Secure String Param for Splunk UF Admin Password"
adminPass=$(aws ssm get-parameters --names $adminPassParam --with-decryption --query Parameters[0].Value --output text --region $awsRegion)
if [ $? -ne 0 ]; then
  echo "***Failed: Cannot retrieve SSM Parameter from $adminPassParam" && exit 1
fi

echo "starting Splunk service for the first time"
sudo /opt/splunkforwarder/bin/splunk start --no-prompt --accept-license --answer-yes --seed-passwd $adminPass
if [ $? -ne 0 ]; then
  echo "***Failed: Cannot Start Splunk Forwarder" && exit 1
fi

echo "stopping Splunk"  
sudo /opt/splunkforwarder/bin/splunk stop
if [ $? -ne 0 ]; then
  echo "***Failed: Could not stop Splunk" && exit 1
fi

echo "enabling Splunk service to start at boot"
# For Splunk UF <9.1. Commented out and retained for backwards compatibility
# sudo /opt/splunkforwarder/bin/splunk enable boot-start -user splunk -group splunk 

# For Splunk UF >= 9.1. This should be used for all new deployments.
sudo /opt/splunkforwarder/bin/splunk enable boot-start -user splunkfwd -group splunkfwd 
if [ $? -ne 0 ]; then
  echo "***Failed: Could not configure Splunk UF for boot start" && exit 1
fi
sleep 10

echo "starting splunk systemd service"
sudo systemctl start SplunkForwarder
if [ $? -ne 0 ]; then
  echo "***Failed: Could not start SplunkForwarder service" && exit 1
fi
sleep 10

echo "updating deploymentclient.conf"
cat <<EOT >> /opt/splunkforwarder/etc/system/local/deploymentclient.conf
# Copy file to $SPLUNK_HOME/etc/system/local/deploymentclient.conf on Linux and Win instances

[target-broker:deploymentServer]
targetUri = $splunkDsServerName:$splunkDsServerPort
EOT

echo "restarting splunk systemd service"
sudo systemctl restart SplunkForwarder
if [ $? -ne 0 ]; then
  echo "***Failed: Could not start SplunkForwarder service" && exit 1
fi