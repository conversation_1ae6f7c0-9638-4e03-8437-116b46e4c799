AWSTemplateFormatVersion: '2010-09-09'

Description: Axonius GOV SAAS Dev instance stack v0.0.2

Parameters:
  VPCCidrParam:
    Type: String
    Default: *********/26 #*********/16
    Description: The VPC CIDR

  PublicSubnetParam:
    Type: String
    Default: *********/28 #*********/24
    Description: The public subnet CIDR

  PrivateSubnetParam:
    Type: String
    Default: *********/28 #*********/24
    Description: The private subnet CIDR

  PublicSubnet2Param:
    Type: String
    Default: *********/28 #*********/24
    Description: The second public subnet CIDR(used for ALB AZ)

  RegionSharedResourcesStackNameParameter:
    Type: String
    Default: SaasRegionResources
    Description: The region shared resources stack name

  CompanyNameUniqueParam:
    Type: String
    Default: TestCustomer
    Description: CustomerName

  CustomerEmailParam:
    Type: String
    Default: <EMAIL>
    Description: Customer email

  AxoniusAmiImageParam:
    Type: String
    Description: Master machine ami to use
    Default: ""

  EnableDiskEncParam:
    Type: String
    AllowedValues:
      - "true"
      - "false"
    Default: true
    Description: "Controls whether disk encryption is enabled"

  SdbSecondaryDeviceParam:
    Type: String
    AllowedValues:
      - "true"
      - "false"
    Default: true
    Description: "Set to true if the version is post 4_6_0"

  HostedZoneName:
    Type: String
    AllowedValues:
      - "on.axonius.com"
      - "on.fabricatedinc.com"
      - "on.axoniusfed.com"
    Default: "on.axoniusfed.com"
    Description: The DNS zone name to register the FQDN (on.fabricatedinc.com for dev)

  OriginalPurposeParam:
    Type: String
    AllowedValues:
      - "trial"
      - "poc"
    Default: "trial"
    Description: The original purpose of this setup (whether this is a trial or poc)

  SupportOSSecurityPatchesParam:
    Type: String
    AllowedValues:
      - "true"
      - "false"
    Default: true
    Description: "Should Axonius Support OSSecurity Patches"

  EnvironmentTypeParam:
    Type: String
    Default: prod
    AllowedValues:
      - prod
      - test
      - dev
    Description: Environment for the machine

  FlowLogDestParam:
    Type: String
    Default: ""
    Description: Flow logs bucket, Leave empty for default

  CertificateArnParam:
    Type: String
    Description: Certificate ARN for the ALB, Leave empty for default
    Default: ""

  MasterInstanceEc2TypeParam:
    Type: String
    Default: r6i.2xlarge
    Description: Instance type for axonius master machine

  CentralCoreRoleParam:
    Type: String
    Default: None
    Description: Is this node a regular saas instance (None), a central core or a central core node
    AllowedValues:
      - None
      - Core
      - Node

  CentralCoreBucketArnParam:
    Type: String
    Description: Leave empty for None and Core. Provide Core's bucket arn for Node.
    Default: ""

  RegionAvailabilityZoneParam:
    Type: String
    Description: Default region availability zone per resources.
    Default: "a"
    AllowedValues:
    - "a"
    - "b"
    - "c"

  AppLoadBalancerAdditionalAvailabilityZoneParam:
    Type: String
    Description: Additional availability zone in region per resources .
    Default: "b"
    AllowedValues:
    - "a"
    - "b"
    - "c"

  WafSupportedParam:
    Type: String
    AllowedValues:
      - "true"
      - "false"
    Default: true
    Description: Is ALB associated with WAF.

Metadata:
  AWS::CloudFormation::Interface:
    ParameterGroups:
      - Label:
          default: "Central Core"
        Parameters:
          - CentralCoreRoleParam
          - CentralCoreBucketArnParam
    ParameterLabels:
      CentralCoreRoleParam:
        default: Central core setup params. Do not modify for regular setups

Conditions:
  ProdEnableDiskEnc:
    Fn::Equals:
      - Ref: EnableDiskEncParam
      - true

  SdbSecondaryDevice:
    Fn::Equals:
      - Ref: SdbSecondaryDeviceParam
      - true

  SdbSecondaryDeviceEnc:
    Fn::And:
      - Condition: ProdEnableDiskEnc
      - Condition: SdbSecondaryDevice

  CentralCoreCondition:
    Fn::Equals:
      - Ref: CentralCoreRoleParam
      - Core

  CentralCoreNodeCondition:
    Fn::Equals:
      - Ref: CentralCoreRoleParam
      - Node

  PrimaryRegion:
    Fn::Equals:
      - Ref: "AWS::Region"
      - us-gov-east-1

  ProdAccount:
    Fn::Equals:
      - Ref: AWS::AccountId
      - ************

  EmptyCertArn:
    Fn::Equals:
      - Ref: CertificateArnParam
      - ""

  EmptyFlowLogDest:
    Fn::Equals:
      - Ref: FlowLogDestParam
      - ""

  ProdDefaultRegion:
    Fn::And:
      - Condition: ProdAccount
      - Condition: PrimaryRegion

"Rules": {
  # yaml is impossible to read or write in conditions/rules
  "EmptyCoreParam": {
    "RuleCondition":
      { Fn::Contains: [ [ "None","Core" ], { "Ref": CentralCoreRoleParam } ] },
    "Assertions": [
      {
        "Assert": { "Fn::Equals": [ { "Ref": CentralCoreBucketArnParam }, "" ] },
        "AssertDescription": "CentralCoreBucketArnParam is relevant only to Node role."
      }
    ]
  }
}

Mappings:
  CertificateArnMap:
    Prod:
      Arn: arn:aws-us-gov:acm:us-gov-east-1:************:certificate/186e0002-254c-4ad7-934a-82ae8099f0ba
    NonProd:
      Arn: arn:aws-us-gov:acm:us-gov-east-1:************:certificate/f96aadcb-8d33-4d6e-816e-b139a6590e69
  FlowLogsArn:
    Prod:
      Arn: arn:aws-us-gov:s3:::axonius-flowlogs-bucket-azylfdqu
    NonProd:
      Arn: arn:aws-us-gov:s3:::axonius-flowlogs-bucket-azylfdqu-sandbox

Resources:
  CentralCoreBucket:
    Type: "AWS::S3::Bucket"
    DeletionPolicy: Delete
    Condition: CentralCoreCondition
    Properties:
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256
      AccelerateConfiguration:
        AccelerationStatus: Enabled
      PublicAccessBlockConfiguration:
        BlockPublicAcls: True
        BlockPublicPolicy: True
        IgnorePublicAcls: True
        RestrictPublicBuckets: True

  VPC:
    Type: AWS::EC2::VPC
    Properties:
      CidrBlock: !Sub "${VPCCidrParam}"
      Tags:
        - Key: Name  ## added by AFS
          Value: !Sub "${AWS::StackName}"

  InternetGateway:
    Type: AWS::EC2::InternetGateway

  VPCGatewayAttachment:
    Type: AWS::EC2::VPCGatewayAttachment
    Properties:
      InternetGatewayId:
        Ref: InternetGateway
      VpcId: !Ref VPC

  PublicSubnet:
    Type: AWS::EC2::Subnet
    Properties:
      AvailabilityZone: !Sub "${AWS::Region}${RegionAvailabilityZoneParam}"
      CidrBlock: !Sub "${PublicSubnetParam}"
      VpcId: !Ref VPC
      Tags:
        - Key: Name
          Value: !Sub Public-${AWS::StackName}

  PrivateSubnet:
    Type: AWS::EC2::Subnet
    Properties:
      AvailabilityZone: !Sub "${AWS::Region}${RegionAvailabilityZoneParam}"
      CidrBlock: !Sub "${PrivateSubnetParam}"
      VpcId: !Ref VPC
      Tags:
        - Key: Name
          Value: !Sub Private-${AWS::StackName}

  PublicSubnet2:
    Type: AWS::EC2::Subnet
    Properties:
      AvailabilityZone: !Sub "${AWS::Region}${AppLoadBalancerAdditionalAvailabilityZoneParam}"
      CidrBlock: !Sub "${PublicSubnet2Param}"
      VpcId: !Ref VPC
      Tags:
        - Key: Name
          Value: !Sub Public2-${AWS::StackName}

  RouteTablePublic:
    Type: AWS::EC2::RouteTable
    Properties:
      VpcId: !Ref VPC

  RouteTablePrivate:
    Type: AWS::EC2::RouteTable
    Properties:
      VpcId: !Ref VPC

  RTAssocPublic:
    Type: AWS::EC2::SubnetRouteTableAssociation
    Properties:
      RouteTableId: !Ref RouteTablePublic
      SubnetId: !Ref PublicSubnet

  RTAssocPublic2:
    Type: AWS::EC2::SubnetRouteTableAssociation
    Properties:
      RouteTableId: !Ref RouteTablePublic
      SubnetId: !Ref PublicSubnet2

  RTAssocPrivate:
    Type: AWS::EC2::SubnetRouteTableAssociation
    Properties:
      RouteTableId: !Ref RouteTablePrivate
      SubnetId: !Ref PrivateSubnet

  GatewayEIP:
    Type: AWS::EC2::EIP
    Properties:
      Domain: vpc

  NatGateway:
    Type: AWS::EC2::NatGateway
    DependsOn:
      - VPCGatewayAttachment
    Properties:
      AllocationId: !GetAtt GatewayEIP.AllocationId
      SubnetId: !Ref PublicSubnet

  RoutePublicToDefaultGw:
    Type: AWS::EC2::Route
    DependsOn:
      - VPCGatewayAttachment
    Properties:
      RouteTableId: !Ref RouteTablePublic
      DestinationCidrBlock: 0.0.0.0/0
      GatewayId: !Ref InternetGateway

  RouteAToNATGw:
    Type: AWS::EC2::Route
    DependsOn:
      - NatGateway
    Properties:
      RouteTableId: !Ref RouteTablePrivate
      DestinationCidrBlock: 0.0.0.0/0
      NatGatewayId: !Ref NatGateway

  MasterEndpointSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Enable all traffic access
      SecurityGroupIngress:
        - CidrIp: 0.0.0.0/0
          IpProtocol: -1
          Description: Allow all inbound traffic
      SecurityGroupEgress:
        - CidrIp: 0.0.0.0/0
          IpProtocol: -1
          Description: Allow all outbound traffic
      VpcId: !Ref VPC

  MasterSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Enable minimal access to the instance
      SecurityGroupIngress:
        - CidrIp: !Sub "${VPCCidrParam}"
          IpProtocol: tcp
          FromPort: 22
          ToPort: 22
          Description: Allow ssh tunnel health check
        - CidrIp: !Sub "${VPCCidrParam}"
          IpProtocol: tcp
          FromPort: 2212
          ToPort: 2212
          Description: Allow tunneler
        - CidrIp: !Sub "${VPCCidrParam}"
          IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          Description: Allow web
        - CidrIp: **********/22   # added by AFS
          IpProtocol: tcp
          FromPort: 5432
          ToPort: 5432
          Description: Allow security scanning
        - CidrIp: **********/22   # added by AFS
          IpProtocol: tcp
          FromPort: 3306
          ToPort: 3306
          Description: Allow security scanning
      VpcId: !Ref VPC

  MasterIamRole:
    Type: AWS::IAM::Role
    Properties:
      Policies:
        - PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - kms:Decrypt
                  - kms:DescribeKey
                  - kms:Encrypt
                  - kms:GenerateDataKey
                Resource:
                  Fn::ImportValue: !Sub "${RegionSharedResourcesStackNameParameter}-SaasParamKmsArn"
              - Effect: Allow
                Action:
                  - kms:Decrypt
                  - kms:DescribeKey
                Resource: !Sub arn:${AWS::Partition}:kms:*:${AWS::AccountId}:key/*
                Condition:
                  ForAnyValue:StringLike:
                    kms:ResourceAliases: !If [ProdAccount, "alias/s3-ax-platform-static-files-prod", "alias/s3-ax-platform-static-files-dev"]
              - Effect: Allow
                Action:
                  - ssm:PutParameter
                Resource: !Sub arn:${AWS::Partition}:ssm:*:*:parameter/stacks/${AWS::StackName}/*
              - Effect: Allow
                Action:
                  - secretsmanager:GetResourcePolicy
                  - secretsmanager:GetSecretValue
                  - secretsmanager:DescribeSecret
                  - secretsmanager:ListSecretVersionIds
                  - secretsmanager:PutSecretValue
                Resource: !Sub arn:${AWS::Partition}:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:/stacks/${AWS::StackName}/*
              - Effect: Allow
                Action:
                  - ssm:GetParametersByPath
                  - ssm:DescribeParameters
                Resource: "*"
              - Effect: Allow
                Action:
                  - ec2:ModifyInstanceMetadataOptions
                Resource: "*"
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:ListBucket
                Resource:
                  - !If [ProdAccount, "arn:aws-us-gov:s3:::s3-ax-platform-static-files-prod", "arn:aws-us-gov:s3:::s3-ax-platform-static-files-dev"]
                  - !If [ProdAccount, "arn:aws-us-gov:s3:::s3-ax-platform-static-files-prod/*", "arn:aws-us-gov:s3:::s3-ax-platform-static-files-dev/*"]
          PolicyName: !Sub parameters_${AWS::StackName}
        - PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - s3:PutObject
                  - s3:ListBucket
                  - kms:GenerateDataKey
                  - kms:DescribeKey
                  - kms:GetPublicKey
                Resource:
                  - !If [ProdAccount, "arn:aws-us-gov:s3:::tf-state-afs","arn:aws-us-gov:s3:::tf-state-afs-dev"]
                  - !If [ProdAccount, "arn:aws-us-gov:s3:::tf-state-afs/*","arn:aws-us-gov:s3:::tf-state-afs-dev/*"]
          PolicyName: !Sub s3_${AWS::StackName}
        - Fn::If:
            - CentralCoreCondition
            - PolicyDocument:
                Version: '2012-10-17'
                Statement:
                  - Effect: Allow
                    Action:
                      - s3:PutObject
                      - s3:GetObject
                      - s3:PutObjectTagging
                      - s3:DeleteObject
                    Resource: !Sub
                      - '${BucketArn}/*'
                      - { BucketArn: !GetAtt CentralCoreBucket.Arn }
                  - Effect: Allow
                    Action:
                      - s3:ListBucket
                    Resource: !GetAtt CentralCoreBucket.Arn
              PolicyName: !Sub core_${ AWS::StackName }
            - Ref: AWS::NoValue
        - Fn::If:
            - CentralCoreNodeCondition
            - PolicyDocument:
                Version: '2012-10-17'
                Statement:
                  - Effect: Allow
                    Action:
                      - s3:PutObject
                      - s3:GetObject
                      - s3:PutObjectTagging
                      - s3:DeleteObject
                    Resource: !Sub '${CentralCoreBucketArnParam}/*'
                  - Effect: Allow
                    Action:
                      - s3:ListBucket
                    Resource: !Sub '${CentralCoreBucketArnParam}'
              PolicyName: !Sub node_${ AWS::StackName }
            - Ref: AWS::NoValue

      ManagedPolicyArns:
        - !Sub arn:${AWS::Partition}:iam::aws:policy/AmazonSSMManagedInstanceCore
      AssumeRolePolicyDocument:
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - ec2.amazonaws.com
            Action:
              - sts:AssumeRole

  MasterInstanceProfile:
    Type: AWS::IAM::InstanceProfile
    Properties:
      Path: "/"
      Roles:
        - !Ref MasterIamRole

  MasterEc2Instance:
    Type: AWS::EC2::Instance
    Properties:
      IamInstanceProfile: !Ref MasterInstanceProfile
      ImageId: !Ref AxoniusAmiImageParam
      InstanceType: !Ref MasterInstanceEc2TypeParam
      SubnetId: !Ref PrivateSubnet
      AvailabilityZone: !Sub "${AWS::Region}${RegionAvailabilityZoneParam}"
      PropagateTagsToVolumeOnCreation: true   # added by AFS
      BlockDeviceMappings:
        - Fn::If:
            - ProdEnableDiskEnc
            - DeviceName: /dev/sda1 # this is controlled by our export process
              Ebs:
                Encrypted: true
                VolumeType: gp3
                Iops: 500
            - Ref: AWS::NoValue
        - Fn::If:
            - SdbSecondaryDeviceEnc
            - DeviceName: /dev/sdf # this is controlled by our export process
              Ebs:
                Encrypted: true
                VolumeType: gp3
                Iops: 1500
            - Ref: AWS::NoValue
        - Fn::If:
            - SdbSecondaryDeviceEnc
            - DeviceName: /dev/sdb # this is controlled by our export process
              Ebs:
                Encrypted: true
                VolumeType: gp3
                Iops: 1500
            - Ref: AWS::NoValue
      SecurityGroupIds:
        - !GetAtt MasterSecurityGroup.GroupId

      Tags:
        - Key: ax-support-os-security-patches
          Value: !Ref SupportOSSecurityPatchesParam
        - Key: Name
          Value: !Sub ${AWS::StackName}
        - Key: company_name
          Value: !Ref CompanyNameUniqueParam
        - Key: customer_name
          Value: !Ref CompanyNameUniqueParam
        - Key: env_type
          Value: !Ref EnvironmentTypeParam
        - Key: customer_email
          Value: !Ref CustomerEmailParam
        - Key: original_purpose
          Value: !Ref OriginalPurposeParam

      UserData:
        Fn::Base64:
          "Fn::Join": [
              "",
            [
                "AXONIUS_SAAS_NODE=True\n",
              {
                "Fn::Sub": "COMPANY_FOR_SIGNUP=${CompanyNameUniqueParam}\n"
              },
              {
                "Fn::Sub": "EMAIL_FOR_SIGNUP=${CustomerEmailParam}\n"
              },
              {
                "Fn::Sub": "WEB_URL=${CompanyNameUniqueParam}.${HostedZoneName}\n"
              },
              {
                "Fn::Sub": "TUNNEL_URL=tun-${CompanyNameUniqueParam}.${HostedZoneName}\n"
              },
              {
                "Fn::Sub": "STACK_NAME=${AWS::StackName}\n"
              },
              {
                "Fn::Sub": [ "PARAMS_KEY_ARN=${KmsKeyForParams}\n",
                             { "KmsKeyForParams": { "Fn::ImportValue": { Fn::Sub: "${RegionSharedResourcesStackNameParameter}-SaasParamKmsArn" } } } ]
              },
              {
                "Fn::Sub": "MACHINE_ENVIRONMENT=${EnvironmentTypeParam}\n"
              },
              {
                "Fn::Sub": "ORIGINAL_PURPOSE=${OriginalPurposeParam}\n"
              }
            ]
          ]

  CommandRunner:
    Type: AWSUtility::CloudFormation::CommandRunner
    DependsOn: MasterEc2Instance
    Properties:
      InstanceType: 't3.medium'
      Role: !Ref MasterInstanceProfile
      # When there is no default VPC for a region, the subnet and security group ids must be specified.
      # This is the case for ax-prod-saas/us-east-1
      SubnetId: !Ref PrivateSubnet
      SecurityGroupId: !GetAtt MasterSecurityGroup.GroupId
      Command: !Sub
        - >-
          curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64-2.12.1.zip" -o "awscliv2.zip" &&
          unzip -o -qq awscliv2.zip &&
          sudo ./aws/install &&
          /usr/local/bin/aws ec2 --region ${Region} modify-instance-metadata-options --instance-id ${InstanceId}
          --instance-metadata-tags enabled --http-endpoint enabled --http-put-response-hop-limit 2
          --http-tokens required > /command-output.txt
        - InstanceId: !Ref MasterEc2Instance
          Region: !Ref "AWS::Region"

  MasterTcpTargetGroupTunneler:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      Port: 443
      Protocol: TCP
      TargetType: instance
      VpcId: !Ref VPC
      Targets:
        - Id: !Ref MasterEc2Instance
          Port: 2212
      HealthCheckPort: 22 # don't spam our openvpn, but work with sshd

  MasterTcpTargetGroupSshBackup:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      Port: 22
      Protocol: TCP
      TargetType: instance
      VpcId: !Ref VPC
      Targets:
        - Id: !Ref MasterEc2Instance
          Port: 22

  AppLoadBalancerTargetGroupHttps:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      Port: 443
      Protocol: HTTPS
      TargetType: instance
      VpcId: !Ref VPC
      Targets:
        - Id: !Ref MasterEc2Instance
          Port: 443
      HealthyThresholdCount: 2
      UnhealthyThresholdCount: 4
      HealthCheckTimeoutSeconds: 30
      HealthCheckIntervalSeconds: 60

  NetworkLoadBalancer:
    DependsOn:
      - VPCGatewayAttachment
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Properties:
      Scheme: internet-facing
      SecurityGroups:
        - !Ref NetworkLoadBalancerSecurityGroup
      Subnets:
        - !Ref PublicSubnet
      Type: network

  ApplicationLoadBalancerListenerHTTPS:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      DefaultActions:
        - TargetGroupArn: !Ref AppLoadBalancerTargetGroupHttps
          Type: forward
      LoadBalancerArn: !Ref ApplicationLoadBalancer
      Port: 443
      Protocol: HTTPS
      Certificates:
        - CertificateArn:
            Fn::If:
              - EmptyCertArn
              - Fn::If:
                  - ProdAccount
                  - !FindInMap [ CertificateArnMap, Prod, Arn ]
                  - !FindInMap [ CertificateArnMap, NonProd, Arn ]
              - !Ref CertificateArnParam
      SslPolicy: ELBSecurityPolicy-TLS13-1-2-FIPS-2023-04

  NetworkLoadBalancerListnerTunneler:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      DefaultActions:
        - TargetGroupArn: !Ref MasterTcpTargetGroupTunneler
          Type: forward
      LoadBalancerArn:
        Ref: NetworkLoadBalancer
      Port: 443
      Protocol: TCP

  ApplicationLoadBalancer:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    DependsOn: VPCGatewayAttachment
    Properties:
      LoadBalancerAttributes:
        - Key: idle_timeout.timeout_seconds
          Value: 600
        - Key: access_logs.s3.enabled
          Value: true
        - Key: access_logs.s3.bucket
          Value:
            Fn::ImportValue: !Sub "${RegionSharedResourcesStackNameParameter}-S3AccessLogsBucketName"
      Scheme: internet-facing
      SecurityGroups:
        - !Ref ApplicationLoadBalancerSecurityGroup
      Subnets:
        - !Ref PublicSubnet
        - !Ref PublicSubnet2
      Type: application

  ApplicationLoadBalancerSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Enable minimal access to the instance
      SecurityGroupIngress:
        - CidrIp: 0.0.0.0/0
          IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          Description: Allow web
      SecurityGroupEgress:
        - IpProtocol: -1 
          CidrIp: !Sub "${VPCCidrParam}"
          Description: Allow Internal
      VpcId: !Ref VPC

  NetworkLoadBalancerSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Enable minimal access to the instance
      SecurityGroupIngress:
        - CidrIp: 0.0.0.0/0
          IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          Description: Allow web
      SecurityGroupEgress:
        - IpProtocol: -1 
          CidrIp: !Sub "${VPCCidrParam}"
          Description: Allow Internal
      VpcId: !Ref VPC

  VpcFlowLog:
    Type: AWS::EC2::FlowLog
    Properties:
      LogDestination:
        Fn::If:
          - EmptyFlowLogDest
          - Fn::If:
              - ProdAccount
              - !FindInMap [ FlowLogsArn, Prod, Arn ]
              - !FindInMap [ FlowLogsArn, NonProd, Arn ]
          - !Ref FlowLogDestParam
      LogDestinationType: s3
      ResourceId: !Ref VPC
      ResourceType: VPC
      TrafficType: ALL

  StackSecretsFile:
    Type: AWS::SecretsManager::Secret
    Properties:
      Description: !Sub "${CompanyNameUniqueParam} secrets file"
      Name: !Sub '/stacks/${AWS::StackName}/secrets_file'
      KmsKeyId:
        Fn::ImportValue: !Sub "${RegionSharedResourcesStackNameParameter}-SaasParamKmsArn"
      SecretString: '{}'

  WAFWebAclAssociation:
    DependsOn: ApplicationLoadBalancer
    Type: AWS::WAFv2::WebACLAssociation
    Properties:
      ResourceArn: !Ref ApplicationLoadBalancer
      WebACLArn:
        Fn::ImportValue: !Sub "${RegionSharedResourcesStackNameParameter}-WAFWebAclArn-Enforcement"

  CloudWatchLogsVPCEndpoint:
    Type: AWS::EC2::VPCEndpoint
    Properties:
      VpcId: !Ref VPC
      ServiceName: !Sub "com.amazonaws.${AWS::Region}.logs"
      VpcEndpointType: Interface
      SubnetIds:
        - !Ref PrivateSubnet
      SecurityGroupIds:
        - !Ref MasterEndpointSecurityGroup
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal: '*'
            Action:
              - logs:CreateLogGroup
              - logs:CreateLogStream
              - logs:PutLogEvents
              - logs:DescribeLogGroups
              - logs:DescribeLogStreams
            Resource: '*'
      PrivateDnsEnabled: true
      Tags:
        - Key: Name
          Value: !Sub "${AWS::StackName}-logs-endpoint"

Outputs:
  VPC:
    Description: VPC
    Value: !Ref VPC

  SubnetPublic:
    Description: Public subnet
    Value: !Ref PublicSubnet

  PrivateSubnet:
    Description: Private subnet
    Value: !Ref PrivateSubnet

  MasterIp:
    Description: Master private IP
    Value: !GetAtt MasterEc2Instance.PrivateIp

  MasterInstanceId:
    Description: Master instance id
    Value: !Ref MasterEc2Instance

  # TunnelUrl:
  #   Description: NetUrl
  #   Value: !Ref AxoniusAliasEPFQDN

  WebUIUrl:
    Description: NetUrl
    Value: !GetAtt ApplicationLoadBalancer.DNSName

  # WebUI:
  #   Description: URL Admin interface
  #   Value: !Sub "https://${AxoniusAliasFQDN}"

  TunnelEP:
    Description: Tunnel Endpoint
    Value: !GetAtt NetworkLoadBalancer.DNSName

  AppLoadBalancerArn:
    Description: "The AppLoadBalancer ARN"
    Value: !Ref ApplicationLoadBalancer

  CentralCoreBucketArn:
    Description: Central core bucket arn
    Condition: CentralCoreCondition
    Value: !GetAtt CentralCoreBucket.Arn
    Export:
      Name: !Sub "${AWS::StackName}-CentralCoreBucketArn"

  CentralCoreBucketName:
    Description: Central core bucket name
    Condition: CentralCoreCondition
    Value: !Ref CentralCoreBucket
    Export:
      Name: !Sub "${AWS::StackName}-CentralCoreBucketName"

  CloudWatchLogsVPCEndpointId:
    Description: CloudWatch Logs VPC Endpoint ID
    Value: !Ref CloudWatchLogsVPCEndpoint
    Export:
      Name: !Sub "${AWS::StackName}-CloudWatchLogsVPCEndpointId"

  # Route53HealthCheckArn:
  #   Description: "The AppLoadBalancer ARN"
  #   Value: !Join [ "/", [arn:aws:route53:::healthcheck, !Ref Route53HealthCheck] ]