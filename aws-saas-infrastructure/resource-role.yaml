AWSTemplateFormatVersion: "2010-09-09"
Description: >
  This CloudFormation template creates a role assumed by CloudFormation
  during CRUDL operations to mutate resources on behalf of the customer.

Resources:
  ExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      MaxSessionDuration: 8400
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: resources.cloudformation.amazonaws.com
            Action: sts:AssumeRole

      Path: "/"
      Policies:
        - PolicyName: ResourceTypePolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                - "cloudformation:CreateStack"
                - "cloudformation:DeleteStack"
                - "cloudformation:DescribeStacks"
                - "ec2:AuthorizeSecurityGroupEgress"
                - "ec2:AuthorizeSecurityGroupIngress"
                - "ec2:CreateSecurityGroup"
                - "ec2:CreateTags"
                - "ec2:DeleteSecurityGroup"
                - "ec2:DescribeInstances"
                - "ec2:DescribeSecurityGroups"
                - "ec2:DescribeSubnets"
                - "ec2:DescribeVpcs"
                - "ec2:RevokeSecurityGroupEgress"
                - "ec2:RevokeSecurityGroupIngress"
                - "ec2:RunInstances"
                - "ec2:TerminateInstances"
                - "iam:GetInstanceProfile"
                - "iam:PassRole"
                - "iam:SimulatePrincipalPolicy"
                - "kms:Decrypt"
                - "kms:Encrypt"
                - "logs:CreateLogStream"
                - "logs:DescribeLogGroups"
                - "log:PutLogEvents"
                - "cloudwatch:PutMetricData"
                - "ssm:DeleteParameter"
                - "ssm:GetParameter"
                - "ssm:PutParameter"
                - "sts:GetCallerIdentity"
                Resource: "*"
Outputs:
  ExecutionRoleArn:
    Value:
      Fn::GetAtt: ExecutionRole.Arn
