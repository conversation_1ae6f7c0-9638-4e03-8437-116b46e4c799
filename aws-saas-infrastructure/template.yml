AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: AWS SAM template for the AWSUtility::CloudFormation::CommandRunner resource type

Globals:
  Function:
    Timeout: 60  # docker start-up times can be long for SAM CLI

Resources:
  TypeFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: software.awsutility.cloudformation.commandrunner.HandlerWrapper::handleRequest
      Runtime: java8
      CodeUri: ./target/awsutility-cloudformation-commandrunner-handler-1.0-SNAPSHOT.jar

  TestEntrypoint:
    Type: AWS::Serverless::Function
    Properties:
      Handler: software.awsutility.cloudformation.commandrunner.HandlerWrapper::testEntrypoint
      Runtime: java8
      CodeUri: ./target/awsutility-cloudformation-commandrunner-handler-1.0-SNAPSHOT.jar
