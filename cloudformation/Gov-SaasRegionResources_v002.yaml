AWSTemplateFormatVersion: 2010-09-09
Description: >
Axonius GOV SAAS Regional resources stack v0.0.2

Parameters:

  Environment:
    Type: String
    Description: The environment type
    AllowedValues:
      - dev
      - production
    Default: production

Conditions:
  ProdMachineCondition:
    Fn::Equals:
      - Ref: Environment
      - production

Resources:
  #############
  # control permissions
  #############
  SaasControlPolicy:
    Type: 'AWS::IAM::ManagedPolicy'
    Properties:
      ManagedPolicyName: saas-control-policy
      Description: 'Policy that give saas control machine access to do its job'
      PolicyDocument: !Sub |
        {
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Sid": "ParametersAndControlSecrets",
                    "Effect": "Allow",
                    "Action": [
                        "ssm:PutParameter",
                        "ssm:DeleteParameter",
                        "secretsmanager:GetSecretValue",
                        "ssm:GetParameterHistory",
                        "kms:DescribeKey",
                        "ssm:GetParametersByPath",
                        "ssm:GetParameters",
                        "ssm:GetParameter",
                        "ssm:DeleteParameter",
                        "ssm:AddTagsToResource"
                    ],
                    "Resource": [
                        "arn:aws:ssm:*:*:parameter/stacks/*",
                        "arn:aws:ssm:*:*:parameter/service/*",
                        "arn:aws:secretsmanager:*:${AWS::AccountId}:secret:saas-control-*"
                    ]
                },
                {
                    "Sid": "KMSCreateKey",
                    "Effect": "Allow",
                    "Action": [
                        "kms:Create*",
                        "kms:Describe*",
                        "kms:Enable*",
                        "kms:List*",
                        "kms:Put*",
                        "kms:Update*",
                        "kms:Revoke*",
                        "kms:Disable*",
                        "kms:Get*",
                        "kms:Delete*",
                        "kms:TagResource",
                        "kms:UntagResource",
                        "kms:ScheduleKeyDeletion",
                        "kms:CancelKeyDeletion",
                        "kms:ReEncryptFrom",
                        "kms:GenerateDataKey",
                        "kms:GenerateDataKeyPair",
                        "kms:GenerateDataKeyPairWithoutPlaintext",
                        "kms:GenerateDataKeyWithoutPlaintext",
                        "kms:GenerateMac",
                        "kms:GenerateRandom",
                        "kms:RetireGrant",
                        "kms:Decrypt"
                    ],
                    "Resource": "*"
                },
                {
                    "Sid": "MachineSecrets",
                    "Effect": "Allow",
                    "Action": [
                        "secretsmanager:CreateSecret",
                        "secretsmanager:DeleteSecret",
                        "secretsmanager:TagResource",
                        "secretsmanager:UpdateSecret",
                        "secretsmanager:UntagResource"
                    ],
                    "Resource": [
                        "arn:aws:secretsmanager:*:${AWS::AccountId}:secret:/stacks/*"
                    ]
                },
                {
                    "Sid": "AWSServices",
                    "Effect": "Allow",
                    "Action": [
                        "iam:*",
                        "route53resolver:CreateResolverEndpoint",
                        "logs:CreateLogDelivery",
                        "iam:CreateServiceLinkedRole",
                        "route53domains:RegisterDomain",
                        "route53:*",
                        "ec2:*",
                        "acm:ListCertificates",
                        "cloudformation:*",
                        "elasticloadbalancing:*",
                        "logs:DeleteLogDelivery",
                        "autoscaling:*",
                        "tag:GetResources",
                        "shield:GetSubscriptionState",
                        "shield:CreateProtection",
                        "shield:DescribeSubscription",
                        "shield:ListProtections",
                        "servicequotas:ListServiceQuotas"
                    ],
                    "Resource": "*"
                },
                {
                    "Sid": "Certs",
                    "Effect": "Allow",
                    "Action": "acm:ListCertificates",
                    "Resource": [
                        "arn:aws:secretsmanager:*:${AWS::AccountId}:secret:saas-control-*",
                        "arn:aws:ssm:*:*:parameter/stacks/*"
                    ]
                },
                {
                    "Sid": "SSM",
                    "Effect": "Allow",
                    "Action": ["ssm:SendCommand", "ssm:GetCommandInvocation"],
                    "Resource": [
                        "arn:aws:ec2:*:${AWS::AccountId}:instance/*",
                        "arn:aws:ssm:*:${AWS::AccountId}:document/SaasRegionResources-SsmCommandRegisterCustomer-*",
                        "arn:aws:ssm:*:${AWS::AccountId}:*",
                        "arn:aws:ssm:*::document/AWS-RunShellScript"
                    ]
                },
                {
                  "Sid": "Shield",
                  "Effect": "Allow",
                  "Action": [
                      "shield:DescribeProtection",
                      "shield:DeleteProtection",
                      "shield:UntagResource",
                      "shield:TagResource",
                      "shield:AssociateHealthCheck",
                      "shield:DisassociateHealthCheck"
                  ],
                  "Resource": [
                      "arn:aws:shield:*:${AWS::AccountId}:protection/*"
                  ]
              },
              {
                  "Sid": "WafV2",
                  "Effect": "Allow",
                  "Action": [
                      "wafv2:GetWebACLForResource",
                      "wafv2:AssociateWebACL",
                      "wafv2:DisassociateWebACL",
                      "wafv2:GetWebACL"
                  ],
                  "Resource": [
                      "arn:aws:wafv2:*:${AWS::AccountId}:regional/webacl/*/*"
                  ]
                },
                {
                  "Sid": "AmiArchiveMisc",
                  "Effect": "Allow",
                  "Action": [
                      "ebs:CompleteSnapshot",
                      "ebs:GetSnapshotBlock",
                      "ebs:ListChangedBlocks",
                      "ebs:ListSnapshotBlocks",
                      "ebs:PutSnapshotBlock",
                      "ebs:StartSnapshot",
                      "backup:DeleteRecoveryPoint"
                  ],
                  "Resource": "*"
                },
                {
                  "Sid": "AmiArchiveKmsKey",
                  "Effect": "Allow",
                  "Action": [
                      "kms:GenerateDataKey",
                      "kms:Encrypt",
                      "kms:Decrypt"
                  ],
                  "Resource": [
                      "arn:aws:kms:*:${AWS::AccountId}:key/*"
                  ],
                  "Condition": {
                      "ForAnyValue:StringLike": {
                          "kms:ResourceAliases": "alias/ami-archive*"
                      }
                  }
                },
                {
                  "Sid": "AmiArchiveS3Bucket",
                  "Effect": "Allow",
                  "Action": [
                      "s3:ListBucket",
                      "s3:PutObject",
                      "s3:PutObjectTagging",
                      "s3:GetBucketLocation"
                  ],
                  "Resource": [
                      "arn:aws:s3:::ami-archive*"
                  ]
                }
            ]
        }
  ControlMachineRole:
    Type: 'AWS::IAM::Role'
    DependsOn: SaasControlPolicy
    Properties:
      AssumeRolePolicyDocument:
        Version: 2012-10-17
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - ec2.amazonaws.com
            Action:
              - 'sts:AssumeRole'
      Description: 'Saas control machine role'
      RoleName: 'control-machine-role'
      ManagedPolicyArns:
        - !Ref SaasControlPolicy
        - arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore
      Tags:
        - Key: CreatedBy
          Value: "cloudformation"
        - Key: Environment
          Value: !Ref Environment
        - Key: ax:business-importance
          Value: !If [ProdMachineCondition, "high", "low"]
        - Key: ax:business-owner
          Value: rnd
        - Key: ax:business-process
          Value: rnd
        - Key: ax:classification
          Value: confidential-restricted
        - Key: ax:creator
          Value: saas-control
        - Key: ax:customer-type
          Value: poc
        - Key: ax:environment
          Value: !Ref Environment
        - Key: ax:lifecycle-type
          Value: cloudformation
        - Key: ax:resource-type
          Value: permanent
        - Key: ax:system-name
          Value: cortex
        - Key: ax:system-type
          Value: customer
        - Key: ax:technical-owner
          Value: devops

  #############
  # control secrets
  #############
  SaasControlEncKey:
    DependsOn: ControlMachineRole
    Type: AWS::KMS::Key
    Properties:
      Description: 'Key for saas control service'
      EnableKeyRotation: true
      KeyPolicy:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              "AWS": { "Fn::Sub": "arn:${AWS::Partition}:iam::${AWS::AccountId}:root" }
            Action:
              - kms:*
            Resource: "*"
          - Effect: Allow
            Principal:
              "AWS": { "Fn::Sub": "arn:aws:iam::${AWS::AccountId}:role/control-machine-role" }
            Action:
              - kms:*
            Resource: "*"
      Tags:
        - Key: CreatedBy
          Value: "cloudformation"
        - Key: Environment
          Value: !Ref Environment
        - Key: ax:business-importance
          Value: !If [ProdMachineCondition, "high", "low"]
        - Key: ax:business-owner
          Value: rnd
        - Key: ax:business-process
          Value: rnd
        - Key: ax:classification
          Value: confidential-restricted
        - Key: ax:creator
          Value: saas-control
        - Key: ax:customer-type
          Value: poc
        - Key: ax:environment
          Value: !Ref Environment
        - Key: ax:lifecycle-type
          Value: cloudformation
        - Key: ax:resource-type
          Value: permanent
        - Key: ax:system-name
          Value: cortex
        - Key: ax:system-type
          Value: customer
        - Key: ax:technical-owner
          Value: devops

  SaasControlEncKeyAlias:
    Type: AWS::KMS::Alias
    Properties:
      AliasName: !Sub "alias/SaasControlEncKey-${Environment}"
      TargetKeyId: !Ref SaasControlEncKey

  SaasControlInstallSecrets:
    Type: AWS::SecretsManager::Secret
    Properties:
      Description: 'Secrets needed to install saas control service and other tools'
      Name: 'saas-control-install'
      KmsKeyId: !Ref SaasControlEncKey
      SecretString: '<placeholder>'
      Tags:
        - Key: CreatedBy
          Value: "cloudformation"
        - Key: Environment
          Value: !Ref Environment
        - Key: ax:business-importance
          Value: !If [ProdMachineCondition, "high", "low"]
        - Key: ax:business-owner
          Value: rnd
        - Key: ax:business-process
          Value: rnd
        - Key: ax:classification
          Value: confidential-restricted
        - Key: ax:creator
          Value: saas-control
        - Key: ax:customer-type
          Value: poc
        - Key: ax:environment
          Value: !Ref Environment
        - Key: ax:lifecycle-type
          Value: cloudformation
        - Key: ax:resource-type
          Value: permanent
        - Key: ax:system-name
          Value: cortex
        - Key: ax:system-type
          Value: customer
        - Key: ax:technical-owner
          Value: devops

  SaasControlServiceSecrets:
    Type: AWS::SecretsManager::Secret
    Properties:
      Description: 'Placeholder to place the json that contains secrets for saas service itself'
      Name: 'saas-control-secrets'
      KmsKeyId: !Ref SaasControlEncKey
      SecretString: '{"BOT_USER_OAUTH_TOKEN": "xoxp-"}'
      Tags:
        - Key: CreatedBy
          Value: "cloudformation"
        - Key: Environment
          Value: !Ref Environment
        - Key: ax:business-importance
          Value: !If [ProdMachineCondition, "high", "low"]
        - Key: ax:business-owner
          Value: rnd
        - Key: ax:business-process
          Value: rnd
        - Key: ax:classification
          Value: confidential-restricted
        - Key: ax:creator
          Value: saas-control
        - Key: ax:customer-type
          Value: poc
        - Key: ax:environment
          Value: !Ref Environment
        - Key: ax:lifecycle-type
          Value: cloudformation
        - Key: ax:resource-type
          Value: permanent
        - Key: ax:system-name
          Value: cortex
        - Key: ax:system-type
          Value: customer
        - Key: ax:technical-owner
          Value: devops

  SaasControlUsersSecrets:
    Type: AWS::SecretsManager::Secret
    Properties:
      Description: 'Placeholder to place the json that describes saas-control service users and permissions'
      Name: 'saas-control-users'
      KmsKeyId: !Ref SaasControlEncKey
      SecretString: '<placeholder>'
      Tags:
        - Key: CreatedBy
          Value: "cloudformation"
        - Key: Environment
          Value: !Ref Environment
        - Key: ax:business-importance
          Value: !If [ProdMachineCondition, "high", "low"]
        - Key: ax:business-owner
          Value: rnd
        - Key: ax:business-process
          Value: rnd
        - Key: ax:classification
          Value: confidential-restricted
        - Key: ax:creator
          Value: saas-control
        - Key: ax:customer-type
          Value: poc
        - Key: ax:environment
          Value: !Ref Environment
        - Key: ax:lifecycle-type
          Value: cloudformation
        - Key: ax:resource-type
          Value: permanent
        - Key: ax:system-name
          Value: cortex
        - Key: ax:system-type
          Value: customer
        - Key: ax:technical-owner
          Value: devops

Outputs:
  ControlMachineRole:
    Description: "Control machine role"
    Value: !Ref ControlMachineRole
    Export:
      Name: !Sub "${AWS::StackName}-ControlMachineRole"

  ControlMachineRoleArn:
    Description: "Control machine role arn"
    Value: !GetAtt ControlMachineRole.Arn
    Export:
      Name: !Sub "${AWS::StackName}-ControlMachineRoleArn"