AWSTemplateFormatVersion: 2010-09-09
Description: >
  This template creates KMS key, ACM, S3 Access log bucket and S3 Server access
  log bucket for a new region of the axonius_saas setup

Parameters:
  SaasParamKeyName:
    Type: String
    Default: "saas-params-kms"

  Environment:
    Type: String
    Description: The environment type
    AllowedValues:
      - gov-dev
      - gov-prod
    Default: gov-prod

  # SaasAlertTopicArn:
  #   Type: String
  #   Default: arn:aws:sns:us-east-1:************:SaaSAlerts

Mappings:
  EnvironmentConfig:
    gov-prod:
      DomainName: "*.on.axoniusfed.com"
    gov-dev:
      DomainName: "*.on.axoniusgov.com"
  RegionsToElasticLoadBalancingAccountID:
      ## https://docs.aws.amazon.com/elasticloadbalancing/latest/classic/enable-access-logs.html
      us-east-1:
        elbAccountId:  arn:aws:iam::************:root
      us-east-2:
        elbAccountId:  arn:aws:iam::************:root
      us-west-1:
        elbAccountId:  arn:aws:iam::************:root
      us-west-2:
        elbAccountId:  arn:aws:iam::************:root
      af-south-1:
        elbAccountId:  arn:aws:iam::************:root
      ca-central-1:
        elbAccountId:  arn:aws:iam::************:root
      eu-central-1:
        elbAccountId:  arn:aws:iam::************:root
      eu-west-1:
        elbAccountId:  arn:aws:iam::************:root
      eu-west-2:
        elbAccountId:  arn:aws:iam::************:root
      eu-south-1:
        elbAccountId:  arn:aws:iam::************:root
      eu-west-3:
        elbAccountId: arn:aws:iam::************:root
      eu-north-1:
        elbAccountId:  arn:aws:iam::************:root
      ap-east-1:
        elbAccountId:  arn:aws:iam::************:root
      ap-northeast-1:
        elbAccountId:  arn:aws:iam::************:root
      ap-northeast-2:
        elbAccountId:  arn:aws:iam::************:root
      ap-northeast-3:
        elbAccountId:  arn:aws:iam::************:root
      ap-southeast-1:
        elbAccountId:  arn:aws:iam::************:root
      ap-southeast-2:
        elbAccountId:  arn:aws:iam::************:root
      ap-south-1:
        elbAccountId:  arn:aws:iam::************:root
      me-south-1:
        elbAccountId:  arn:aws:iam::************:root
      sa-east-1:
        elbAccountId:  arn:aws:iam::************:root
      us-gov-east-1:
        elbAccountId:  arn:aws-us-gov:iam::************:root
      us-gov-west-1:
        elbAccountId:  arn:aws-us-gov:iam::************:root

Conditions:
  IsUSEast2Region:
    Fn::Equals:
      - Ref: AWS::Region
      - us-east-2

  ProdAccount:
    Fn::Or:
      - Fn::Equals:
          - Ref: AWS::AccountId
          - "************"
      - Fn::Equals:
          - Ref: AWS::AccountId
          - "************"

  LmsAccounts:
    Fn::Or:
      - Fn::Equals:
          - Ref: AWS::AccountId
          - "************"
      - Fn::Equals:
          - Ref: AWS::AccountId
          - "************"

  NotLmsAccounts:
    Fn::Not:
      - Condition: LmsAccounts

  IsUSEast2AndProd:
    Fn::And:
      - Condition: IsUSEast2Region
      - Condition: ProdAccount

  NotIsUSEast2AndProd:
    Fn::Not:
      - Condition: IsUSEast2AndProd

  NotUsEast2AndProdNotLms:
    Fn::And:
      - Condition: NotIsUSEast2AndProd
      - Condition: NotLmsAccounts

  UsEast2AndProdNotLms:
    Fn::And:
      - Condition: IsUSEast2Region
      - Condition: NotLmsAccounts

  ProdMachineCondition:
    Fn::Equals:
      - Ref: Environment
      - prod

  # https://docs.aws.amazon.com/elasticloadbalancing/latest/application/enable-access-logging.html
  AWSRegionsAfter2022:
    Fn::Or:
      - Fn::Equals:
          - Ref: AWS::Region
          - "ap-southeast-4"
      - Fn::Equals:
          - Ref: AWS::Region
          - "ca-west-1"
      - Fn::Equals:
          - Ref: AWS::Region
          - "eu-south-2"
      - Fn::Equals:
          - Ref: AWS::Region
          - "me-central-1"
      - Fn::Equals:
          - Ref: AWS::Region
          - "ap-south-2"
      - Fn::Equals:
          - Ref: AWS::Region
          - "eu-central-2"
      - Fn::Equals:
          - Ref: AWS::Region
          - "il-central-1"

  # https://docs.aws.amazon.com/elasticloadbalancing/latest/application/enable-access-logging.html
  AWSRegionsBefore2022:
    Fn::Not:
      - Condition: AWSRegionsAfter2022

Resources:
  OnAxoniusCertificate:
    Type: AWS::CertificateManager::Certificate
    Properties:
      DomainName: !FindInMap [ EnvironmentConfig, !Ref Environment, DomainName ]
      ValidationMethod: "DNS"
      Tags:
        - Key: CreatedBy
          Value: "cloudformation"
        - Key: Environment
          Value: !Ref Environment
        - Key: ax:business-importance
          Value: !If [ProdMachineCondition, "high", "low"]
        - Key: ax:business-owner
          Value: rnd
        - Key: ax:business-process
          Value: rnd
        - Key: ax:classification
          Value: confidential-restricted
        - Key: ax:creator
          Value: saas-control
        - Key: ax:customer-type
          Value: poc
        - Key: ax:environment
          Value: !Ref Environment
        - Key: ax:lifecycle-type
          Value: cloudformation
        - Key: ax:resource-type
          Value: permanent
        - Key: ax:system-name
          Value: cortex
        - Key: ax:system-type
          Value: customer
        - Key: ax:technical-owner
          Value: devops
  SaasParamKms:
    Type: AWS::KMS::Key
    Properties:
      Description: !Sub "${SaasParamKeyName}-${Environment}"
      Tags:
        - Key: CreatedBy
          Value: "cloudformation"
        - Key: Environment
          Value: !Ref Environment
        - Key: ax:business-importance
          Value: !If [ProdMachineCondition, "high", "low"]
        - Key: ax:business-owner
          Value: rnd
        - Key: ax:business-process
          Value: rnd
        - Key: ax:classification
          Value: confidential-restricted
        - Key: ax:creator
          Value: saas-control
        - Key: ax:customer-type
          Value: poc
        - Key: ax:environment
          Value: !Ref Environment
        - Key: ax:lifecycle-type
          Value: cloudformation
        - Key: ax:resource-type
          Value: permanent
        - Key: ax:system-name
          Value: cortex
        - Key: ax:system-type
          Value: customer
        - Key: ax:technical-owner
          Value: devops
      EnableKeyRotation: true
      KeyPolicy:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              "AWS": { "Fn::Sub": "arn:${AWS::Partition}:iam::${AWS::AccountId}:root" }
            Action:
              - kms:*
            Resource: "*"
          - Effect: Allow
            Principal:
              "AWS": { "Fn::Sub": "arn:aws:iam::${AWS::AccountId}:role/control-machine-role" }
            Action:
              - kms:*
            Resource: "*"
          - Effect: Allow
            Principal:
              "AWS": { "Fn::Sub": "arn:aws:iam::${AWS::AccountId}:role/ax-saas-control-ecs-task-role-api" }
            Action:
              - kms:*
            Resource: "*"
          - Effect: Allow
            Principal:
              "AWS": { "Fn::Sub": "arn:aws:iam::${AWS::AccountId}:role/ax-saas-control-${AWS::AccountId}-role" }
            Action:
              - kms:*
            Resource: "*"

  SaasParamKmsAlias:
    Type: AWS::KMS::Alias
    Properties:
      AliasName: !Sub "alias/${SaasParamKeyName}-${Environment}"
      TargetKeyId: !Ref SaasParamKms


  # Backup ec2 resources
  SaasBackupKMSKey:
    Condition: NotLmsAccounts
    Type: AWS::KMS::Key
    Properties:
      Description: "Encryption key for backups"
      EnableKeyRotation: True
      KeyPolicy:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              "AWS": { "Fn::Sub": "arn:${AWS::Partition}:iam::${AWS::AccountId}:root" }
            Action:
              - kms:*
            Resource: "*"

  SaasBackupKmsAlias:
    Condition: NotLmsAccounts
    Type: AWS::KMS::Alias
    Properties:
      AliasName: !Sub "alias/SaasBackupKMSKey-${Environment}"
      TargetKeyId: !Ref SaasBackupKMSKey

  SaasBackupVaultWithDailyBackups:
    Condition: NotLmsAccounts
    Type: "AWS::Backup::BackupVault"
    Properties:
      BackupVaultName: "SaasBackupVaultWithDailyBackups"
      EncryptionKeyArn: !GetAtt SaasBackupKMSKey.Arn
      Notifications:
        BackupVaultEvents:
          - "BACKUP_JOB_EXPIRED"
          - "BACKUP_JOB_FAILED"
        SNSTopicArn: !Ref SaasAlertTopicArn

  SaasBackupPlanWithThinBackups:
    Condition: NotLmsAccounts
    Type: "AWS::Backup::BackupPlan"
    Properties:
      BackupPlan:
        BackupPlanName: "Backup_axonius_saas_ec2_instances"
        BackupPlanRule:
          - RuleName: "RuleForDailyBackups"
            TargetBackupVault: !Ref SaasBackupVaultWithDailyBackups
            ScheduleExpression: "cron(0 3 ? * * *)"
            Lifecycle:
              DeleteAfterDays: 7
            StartWindowMinutes: 300
            CompletionWindowMinutes: 600

  SaasBackupRole:
    Condition: NotLmsAccounts
    Type: "AWS::IAM::Role"
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: "Allow"
            Principal:
              Service:
                - "backup.amazonaws.com"
            Action:
              - "sts:AssumeRole"
      ManagedPolicyArns:
        - "arn:aws:iam::aws:policy/service-role/AWSBackupServiceRolePolicyForBackup"

  SaasTagBasedBackupSelection:
    Condition: NotUsEast2AndProdNotLms
    Type: "AWS::Backup::BackupSelection"
    Properties:
      BackupSelection:
        SelectionName: "AxoniusSaasMasters"
        IamRoleArn: !GetAtt SaasBackupRole.Arn
        ListOfTags:
          - ConditionType: "STRINGEQUALS"
            ConditionKey: "aws:cloudformation:logical-id"
            ConditionValue: "MasterEc2Instance"
      BackupPlanId: !Ref SaasBackupPlanWithThinBackups
    DependsOn: SaasBackupPlanWithThinBackups

  SaasTagBasedBackupSelectionExcludeUsEast2:
    Condition: UsEast2AndProdNotLms
    Type: "AWS::Backup::BackupSelection"
    Properties:
      BackupSelection:
        SelectionName: "AxoniusSaasMasters"
        IamRoleArn: !GetAtt SaasBackupRole.Arn
        ListOfTags:
          - ConditionType: "STRINGEQUALS"
            ConditionKey: "aws:cloudformation:logical-id"
            ConditionValue: "MasterEc2Instance"
        NotResources:
          - Fn::Sub: "arn:aws:ec2:${AWS::Region}:${AWS::AccountId}:instance/i-0ab887eb544d18d3e"
      BackupPlanId: !Ref SaasBackupPlanWithThinBackups
    DependsOn: SaasBackupPlanWithThinBackups

  SsmCommandRegisterCustomer:
    Type: AWS::SSM::Document
    Properties:
      Content:
        schemaVersion: '2.2'
        description: 'Runs the register customer script'
        parameters:
          companyname:
            type: String
            default: ''
            allowedPattern: "[ 0-9a-zA-Z-_.@:/]*"
          email:
            type: String
            default: ''
            allowedPattern: "[0-9a-zA-Z-_.@:/]*"
          weburl:
            type: String
            default: ''
            allowedPattern: "[0-9a-zA-Z-_.@:/]*"
          originalpurpose:
            type: String
            default: ''
            allowedPattern: "[0-9a-zA-Z-_.@:/]*"
        mainSteps:
          - action: aws:runShellScript
            name: runCommands
            inputs:
              timeoutSeconds: '60'
              runCommand:
                - "/etc/axonius/register_saas.sh --company-name '{{ companyname }}' --customer-email '{{ email }}' --web-url '{{ weburl }}' --original-purpose '{{ originalpurpose }}'"
      DocumentType: Command
      DocumentFormat: "YAML"
      TargetType: "/AWS::EC2::Instance"
      Tags:
        - Key: "Name"
          Value: "RegisterCustomer"
        - Key: ax:business-importance
          Value: !If [ProdMachineCondition, "high", "low"]
        - Key: ax:business-owner
          Value: rnd
        - Key: ax:business-process
          Value: rnd
        - Key: ax:classification
          Value: confidential-restricted
        - Key: ax:creator
          Value: saas-control
        - Key: ax:customer-type
          Value: poc
        - Key: ax:environment
          Value: !Ref Environment
        - Key: ax:lifecycle-type
          Value: cloudformation
        - Key: ax:resource-type
          Value: permanent
        - Key: ax:system-name
          Value: cortex
        - Key: ax:system-type
          Value: customer
        - Key: ax:technical-owner
          Value: devops

  S3ServerAccessLoggingBucketPolicy:
    DependsOn: S3ServerAccessLoggingBucket
    Type: AWS::S3::BucketPolicy
    UpdateReplacePolicy: Retain
    Properties:
      Bucket: !Sub s3-server-access-logging-${AWS::Region}-${AWS::AccountId}
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          Effect: Allow
          Principal:
            Service: logging.s3.amazonaws.com
          Action:
            - 's3:PutObject'
          Resource:
            Fn::Sub: 'arn:aws:s3:::s3-server-access-logging-${AWS::Region}-${AWS::AccountId}/AWSLogs/*'
          Condition:
            ArnLike:
              'aws:SourceArn': 'arn:aws:s3:::*'
            StringEquals:
              'aws:SourceAccount': !Ref "AWS::AccountId"

  ELBAccessLogBucketPolicy:
    DependsOn: ELBAccessLogBucket
    Type: AWS::S3::BucketPolicy
    UpdateReplacePolicy: Retain
    Properties:
      Bucket: !Sub s3-elb-access-logs-${AWS::Region}-${AWS::AccountId}
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: delivery.logs.amazonaws.com
            Action: 's3:PutObject'
            Resource:
              Fn::Sub: 'arn:aws:s3:::s3-elb-access-logs-${AWS::Region}-${AWS::AccountId}/AWSLogs/${AWS::AccountId}/*'
            Condition:
              StringEquals:
                's3:x-amz-acl': bucket-owner-full-control
          - Effect: Allow
            Principal:
              Service: delivery.logs.amazonaws.com
            Action: 's3:GetBucketAcl'
            Resource:
              Fn::Sub: 'arn:aws:s3:::s3-elb-access-logs-${AWS::Region}-${AWS::AccountId}'

  ELBAccessLogBucketPolicyRegionAfter2022:
    DependsOn: ELBAccessLogBucket
    Type: AWS::S3::BucketPolicy
    UpdateReplacePolicy: Retain
    Condition: AWSRegionsAfter2022
    Properties:
      Bucket: !Sub s3-elb-access-logs-${AWS::Region}-${AWS::AccountId}
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: 'logdelivery.elasticloadbalancing.amazonaws.com'
            Action: 's3:PutObject'
            Resource:
              Fn::Sub: 'arn:aws:s3:::s3-elb-access-logs-${AWS::Region}-${AWS::AccountId}/AWSLogs/${AWS::AccountId}/*'

  ELBAccessLogBucketPolicyRegionBefore2022:
    DependsOn: ELBAccessLogBucket
    Type: AWS::S3::BucketPolicy
    UpdateReplacePolicy: Retain
    Condition: AWSRegionsBefore2022
    Properties:
      Bucket: !Sub s3-elb-access-logs-${AWS::Region}-${AWS::AccountId}
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              AWS: !FindInMap [ RegionsToElasticLoadBalancingAccountID, !Ref "AWS::Region", elbAccountId ]
            Action: 's3:PutObject'
            Resource:
              Fn::Sub: 'arn:aws:s3:::s3-elb-access-logs-${AWS::Region}-${AWS::AccountId}/AWSLogs/${AWS::AccountId}/*'

  S3ServerAccessLoggingBucket:
    Type: "AWS::S3::Bucket"
    DeletionPolicy: Retain
    Properties:
      BucketName: !Sub 's3-server-access-logging-${AWS::Region}-${AWS::AccountId}'
      PublicAccessBlockConfiguration:
        BlockPublicAcls: True
        BlockPublicPolicy: True
        IgnorePublicAcls: True
        RestrictPublicBuckets: True
      LifecycleConfiguration:
        Rules:
          - Id: log_expriration
            Status: Enabled
            ExpirationInDays: 30

  ELBAccessLogBucket:
    DependsOn: [S3ServerAccessLoggingBucket, S3ServerAccessLoggingBucketPolicy]
    Type: "AWS::S3::Bucket"
    DeletionPolicy: Retain
    Properties:
      BucketName: !Sub s3-elb-access-logs-${AWS::Region}-${AWS::AccountId}
      LoggingConfiguration:
         DestinationBucketName: !Sub s3-server-access-logging-${AWS::Region}-${AWS::AccountId}
      PublicAccessBlockConfiguration:
        BlockPublicAcls: True
        BlockPublicPolicy: True
        IgnorePublicAcls: True
        RestrictPublicBuckets: True
      LifecycleConfiguration:
        Rules:
          - Id: log_expriration
            Status: Enabled
            ExpirationInDays: 30

  S3LambdaArtifactsBucket:
    Condition: NotLmsAccounts
    Type: "AWS::S3::Bucket"
    DeletionPolicy: Retain
    Properties:
      BucketName: !Sub "lambda-artifacts-${AWS::AccountId}-${AWS::Region}"
      VersioningConfiguration:
        Status: Enabled
      PublicAccessBlockConfiguration:
        BlockPublicAcls: True
        BlockPublicPolicy: True
        IgnorePublicAcls: True
        RestrictPublicBuckets: True

  S3LambdaArtifactsBucketPolicy:
    Condition: NotLmsAccounts
    DependsOn: S3LambdaArtifactsBucket
    Type: AWS::S3::BucketPolicy
    UpdateReplacePolicy: Retain
    Properties:
      Bucket: !Sub "${S3LambdaArtifactsBucket}"
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              AWS: !Sub "arn:aws:iam::${AWS::AccountId}:role/saas-oidc-github-ci"
            Action:
              - s3:PutObject
              - s3:PutObjectTagging
              - s3:ListBucket
            Resource:
              - !Sub "arn:aws:s3:::${S3LambdaArtifactsBucket}"
              - !Sub "arn:aws:s3:::${S3LambdaArtifactsBucket}/*"
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action:
              - s3:GetObject
              - s3:ListBucket
            Resource:
              - !Sub "arn:aws:s3:::${S3LambdaArtifactsBucket}"
              - !Sub "arn:aws:s3:::${S3LambdaArtifactsBucket}/*"
            Condition:
              StringLike:
                'aws:SourceArn': !Sub "arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function/historical-*"
          - Effect: Deny
            Principal:
              AWS: "*"
            Action:
              - s3:PutObject
            Resource:
              - !Sub "arn:aws:s3:::${S3LambdaArtifactsBucket}/*"
            Condition:
              StringNotEquals:
                'aws:PrincipalArn': !Sub "arn:aws:iam::${AWS::AccountId}:role/saas-oidc-github-ci"

  WAFRegexPatternSet:
    Type: AWS::WAFv2::RegexPatternSet
    Properties:
      Description: Common URIs we wish to exclude from WAF for specific rules
      Name: CommonExcludedURIs
      RegularExpressionList:
        - \/api\/([vV]4\.0\/)?adapters\/.*\/connections\/.*\/v2$
        - \/api\/([vV]4\.0\/)?adapters\/.*\/connections\/test$
        - \/api\/([vV]4\.0\/)?adapters\/.*\/connections$
        - \/api\/([vV]4\.0\/)?adapters\/.*\/IngestionRulesSchema$
        - \/api\/([vV]4\.0\/)?queries\/.*
        - \/api\/([vV]4\.0\/)?devices$
        - \/api\/([vV]4\.0\/)?devices\/(count|csv)$
        - \/api\/([vV]4\.0\/)?users\/count$
        - \/api\/([vV]4\.0\/)?users$
        - \/api\/([vV]4\.0\/)?adapters\/.*\/.*\/upload_file$
      Scope: REGIONAL
      Tags:
        - Key: CreatedBy
          Value: "cloudformation"
        - Key: Environment
          Value: !Ref Environment
        - Key: ax:business-importance
          Value: !If [ProdMachineCondition, "high", "low"]
        - Key: ax:business-owner
          Value: rnd
        - Key: ax:business-process
          Value: rnd
        - Key: ax:classification
          Value: confidential-restricted
        - Key: ax:creator
          Value: saas-control
        - Key: ax:customer-type
          Value: poc
        - Key: ax:environment
          Value: !Ref Environment
        - Key: ax:lifecycle-type
          Value: cloudformation
        - Key: ax:resource-type
          Value: permanent
        - Key: ax:system-name
          Value: cortex
        - Key: ax:system-type
          Value: customer
        - Key: ax:technical-owner
          Value: devops

  WAFRegexPatternSetAdapters:
    Type: AWS::WAFv2::RegexPatternSet
    Properties:
      Description: Adapters URIs we wish to exclude from WAF for specific rules
      Name: AdaptersExcludedURIs
      RegularExpressionList:
        - \/api\/([vV]4\.0\/)?adapters\/.*\/connections\/.*\/v2$
        - \/api\/([vV]4\.0\/)?adapters\/.*\/connections\/test$
        - \/api\/([vV]4\.0\/)?adapters\/.*\/connections$
        - \/api\/([vV]4\.0\/)?adapters\/.*\/IngestionRulesSchema$
        - \/api\/([vV]4\.0\/)?adapters\/.*\/.*\/upload_file$
      Scope: REGIONAL
      Tags:
        - Key: CreatedBy
          Value: "cloudformation"
        - Key: Environment
          Value: !Ref Environment
        - Key: ax:business-importance
          Value: !If [ ProdMachineCondition, "high", "low" ]
        - Key: ax:business-owner
          Value: rnd
        - Key: ax:business-process
          Value: rnd
        - Key: ax:classification
          Value: confidential-restricted
        - Key: ax:creator
          Value: saas-control
        - Key: ax:customer-type
          Value: poc
        - Key: ax:environment
          Value: !Ref Environment
        - Key: ax:lifecycle-type
          Value: cloudformation
        - Key: ax:resource-type
          Value: permanent
        - Key: ax:system-name
          Value: cortex
        - Key: ax:system-type
          Value: customer
        - Key: ax:technical-owner
          Value: devops

  WAFWebAcl:
    Type: AWS::WAFv2::WebACL
    Properties:
      DefaultAction:
        Allow: {}
      Description: This is a WAF WebAcl that should be used in all customers ALB
      Name: !Sub ${AWS::Region}-WebAcl
      Scope: REGIONAL
      Rules:
        - Name: AmazonIpReputationList
          Priority: 0
          OverrideAction:
            Count: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: AmazonIpReputationListMetric
          Statement:
            ManagedRuleGroupStatement:
              VendorName: AWS
              Name: AWSManagedRulesAmazonIpReputationList
              ExcludedRules: [ ]
        - Name: AnonymousIpList
          Priority: 1
          OverrideAction:
            Count: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: AnonymousIpListMetric
          Statement:
            ManagedRuleGroupStatement:
              VendorName: AWS
              Name: AWSManagedRulesAnonymousIpList
              RuleActionOverrides:
                # Requested not to inspect as it might block IP coming from hosting providers
                - ActionToUse:
                    Count: { }
                  Name: HostingProviderIPList
              ExcludedRules: [ ]
        - Name: RateLimit
          Priority: 2
          Action:
            Count: {}
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: RateLimitMetric
          Statement:
            RateBasedStatement:
              Limit: 5000
              AggregateKeyType: IP
        - Name: CommonRuleSet
          Priority: 3
          OverrideAction:
            Count: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: CommonRuleSetMetric
          Statement:
            ManagedRuleGroupStatement:
              VendorName: AWS
              Name: AWSManagedRulesCommonRuleSet
              Version: Version_1.6
              RuleActionOverrides:
                 # Requested not to inspect as it might block machine <> machine traffic
                - ActionToUse:
                    Count: { }
                  Name: NoUserAgent_HEADER
                # Count should not be modified as might impact legit traffic
                - ActionToUse:
                    Count: { }
                  Name: SizeRestrictions_BODY
                # Count should not be modified as might impact legit traffic
                - ActionToUse:
                    Count: { }
                  Name: SizeRestrictions_QUERYSTRING
                # Count action should not be changed as we filter and block by AX_Block_EC2MetaDataSSRF_Body rule
                - ActionToUse:
                    Count: { }
                  Name: EC2MetaDataSSRF_BODY
                # Count action should not be changed as we filter and block by AX_Block_GenericRFI_Body rule
                - ActionToUse:
                    Count: { }
                  Name: GenericRFI_BODY
                # Count action should not be changed as we filter and block by AX_Block_GenericLFI_Body rule
                - ActionToUse:
                    Count: { }
                  Name: GenericLFI_BODY
                # Count action should not be changed as we filter and block by AX_Block_EC2MetaDataSSRF_QueryArguments rule
                - ActionToUse:
                    Count: { }
                  Name: EC2MetaDataSSRF_QUERYARGUMENTS
                # Count action should not be changed as we filter and block by AX_Block_CrossSiteScripting_Body rule
                - ActionToUse:
                    Count: { }
                  Name: CrossSiteScripting_BODY
                # Count action should not be changed as we filter and block by AX_Block_GenericRFI_QueryArguments rule
                - ActionToUse:
                    Count: { }
                  Name: GenericRFI_QUERYARGUMENTS
                # Count action should not be changed as we filter and block by AX_Block_GenericRFI_Cookie rule
                - ActionToUse:
                    Count: { }
                  Name: EC2MetaDataSSRF_COOKIE
                # Count action should not be changed as we filter and block by AX_Block_GenericRFI_UriPath rule
                - ActionToUse:
                    Count: { }
                  Name: EC2MetaDataSSRF_URIPATH
              ExcludedRules: [ ]
        - Name: KnownBadInputsRuleSet
          Priority: 4
          OverrideAction:
            Count: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: KnownBadInputsRuleSetMetric
          Statement:
            ManagedRuleGroupStatement:
              VendorName: AWS
              Name: AWSManagedRulesKnownBadInputsRuleSet
              Version: Version_1.16
              RuleActionOverrides:
                - ActionToUse: # Requested not to inspect
                    Count: { }
                  Name: JavaDeserializationRCE_BODY
                - ActionToUse: # Requested not to inspect
                    Count: { }
                  Name: JavaDeserializationRCE_URIPATH
                - ActionToUse: # Requested not to inspect
                    Count: { }
                  Name: JavaDeserializationRCE_QUERYSTRING
                - ActionToUse: # Requested not to inspect
                    Count: { }
                  Name: JavaDeserializationRCE_HEADER
                - ActionToUse: # Requested not to inspect
                    Count: { }
                  Name: Log4JRCE_QUERYSTRING
                - ActionToUse: # Requested not to inspect
                    Count: { }
                  Name: Log4JRCE_BODY
                - ActionToUse: # Requested not to inspect
                    Count: { }
                  Name: Log4JRCE_HEADER
                - ActionToUse: # Requested not to inspect
                    Count: { }
                  Name: Log4JRCE_URIPATH
              ExcludedRules: [ ]
        - Name: RulesLinuxRuleSet
          Priority: 5
          OverrideAction:
            Count: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: RulesLinuxRuleSetMetric
          Statement:
            ManagedRuleGroupStatement:
              VendorName: AWS
              Name: AWSManagedRulesLinuxRuleSet
              Version: !If [AWSRegionsAfter2022, "Version_2.1", "Version_2.2"]
              RuleActionOverrides:
                - ActionToUse: # Count action should not be changed as we filter and block by AX_Block_LFI_QueryString rule
                    Count: { }
                  Name: LFI_QUERYSTRING
              ExcludedRules: [ ]
        - Name: AX_Block_GenericRFI_Body
          Priority: 6
          Action:
            Count: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: AXBlockGenericRFIBody
          Statement:
            AndStatement:
              Statements:
              - LabelMatchStatement:
                  Key: awswaf:managed:aws:core-rule-set:GenericRFI_Body
                  Scope: LABEL
              - NotStatement:
                  Statement:
                    RegexPatternSetReferenceStatement:
                      Arn: !GetAtt WAFRegexPatternSet.Arn
                      FieldToMatch:
                        UriPath: {}
                      TextTransformations:
                      - Priority: 0
                        Type: NONE
        - Name: AX_Block_GenericLFI_Body
          Priority: 7
          Action:
            Count: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: AXBlockGenericLFIBody
          Statement:
            AndStatement:
              Statements:
                - LabelMatchStatement:
                    Key: awswaf:managed:aws:core-rule-set:GenericLFI_Body
                    Scope: LABEL
                - NotStatement:
                    Statement:
                      AndStatement:
                        Statements:
                          - RegexMatchStatement:
                              RegexString: \/api\/([vV]4\.0\/)?adapters/nmap_adapter/upload_file$
                              FieldToMatch:
                                UriPath: { }
                              TextTransformations:
                                - Priority: 0
                                  Type: NONE
                          - ByteMatchStatement:
                              FieldToMatch:
                                Body:
                                  OversizeHandling: CONTINUE
                              PositionalConstraint: CONTAINS
                              SearchString: 'content-type: text/xml'
                              TextTransformations:
                                - Priority: 0
                                  Type: LOWERCASE
                          - ByteMatchStatement:
                              FieldToMatch:
                                Body:
                                  OversizeHandling: CONTINUE
                              PositionalConstraint: CONTAINS
                              SearchString: '<?xml'
                              TextTransformations:
                                - Priority: 0
                                  Type: LOWERCASE
        - Name: AX_Block_EC2MetaDataSSRF_Body
          Priority: 8
          Action:
            Count: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: AXBlockEC2MetaDataSSRFBody
          Statement:
            AndStatement:
              Statements:
                - LabelMatchStatement:
                    Key: awswaf:managed:aws:core-rule-set:EC2MetaDataSSRF_Body
                    Scope: LABEL
                - RegexMatchStatement:
                    RegexString: \/api\/([vV]4\.0\/)?(?:enforcements|adapters)\/.*
                    FieldToMatch:
                      UriPath: {}
                    TextTransformations:
                    - Priority: 0
                      Type: NONE
                - NotStatement:
                    Statement:
                      RegexPatternSetReferenceStatement:
                        Arn: !GetAtt WAFRegexPatternSetAdapters.Arn
                        FieldToMatch:
                          UriPath: { }
                        TextTransformations:
                          - Priority: 0
                            Type: NONE
                - NotStatement:
                    Statement:
                      ByteMatchStatement:
                        FieldToMatch:
                          Body:
                            OversizeHandling: CONTINUE
                        PositionalConstraint: CONTAINS
                        SearchString: '"action":{"action_name":"send_data_to_ms_power_bi"'
                        TextTransformations:
                          - Priority: 0
                            Type: LOWERCASE
        - Name: AX_Block_EC2MetaDataSSRF_QueryArguments
          Priority: 9
          Action:
            Count: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: AXBlockEC2MetaDataSSRFQueryArguments
          Statement:
            AndStatement:
              Statements:
                - LabelMatchStatement:
                    Key: awswaf:managed:aws:core-rule-set:EC2MetaDataSSRF_QueryArguments
                    Scope: LABEL
                - RegexMatchStatement:
                    RegexString: \/api\/([vV]4\.0\/)?(?:enforcements|adapters)\/.*
                    FieldToMatch:
                      UriPath: {}
                    TextTransformations:
                    - Priority: 0
                      Type: NONE
                - NotStatement:
                    Statement:
                      RegexPatternSetReferenceStatement:
                        Arn: !GetAtt WAFRegexPatternSetAdapters.Arn
                        FieldToMatch:
                          UriPath: { }
                        TextTransformations:
                          - Priority: 0
                            Type: NONE
        - Name: AX_Block_CrossSiteScripting_Body
          Priority: 10
          Action:
            Count: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: AXBlockCrossSiteScriptingBody
          Statement:
            AndStatement:
              Statements:
                - LabelMatchStatement:
                    Key: awswaf:managed:aws:core-rule-set:CrossSiteScripting_Body
                    Scope: LABEL
                - NotStatement:
                    Statement:
                      AndStatement:
                        Statements:
                          - RegexMatchStatement:
                              RegexString: \/api\/([vV]4\.0\/)?adapters\/nmap_adapter\/upload_file$
                              FieldToMatch:
                                UriPath: { }
                              TextTransformations:
                                - Priority: 0
                                  Type: NONE
                          - ByteMatchStatement:
                              FieldToMatch:
                                Body:
                                  OversizeHandling: CONTINUE
                              PositionalConstraint: CONTAINS
                              SearchString: 'Content-Type: text/xml'
                              TextTransformations:
                                - Priority: 0
                                  Type: LOWERCASE
                          - ByteMatchStatement:
                              FieldToMatch:
                                Body:
                                  OversizeHandling: CONTINUE
                              PositionalConstraint: CONTAINS
                              SearchString: '<?xml'
                              TextTransformations:
                                - Priority: 0
                                  Type: LOWERCASE
                - NotStatement:
                    Statement:
                      AndStatement:
                        Statements:
                          - ByteMatchStatement:
                              FieldToMatch:
                                UriPath: { }
                              PositionalConstraint: EXACTLY
                              SearchString: /api/enforcements/actions/upload_file
                              TextTransformations:
                                - Priority: 0
                                  Type: NONE
                          - ByteMatchStatement:
                              FieldToMatch:
                                Body:
                                  OversizeHandling: CONTINUE
                              PositionalConstraint: CONTAINS
                              SearchString: 'content-type: application/octet-stream'
                              TextTransformations:
                                - Priority: 0
                                  Type: LOWERCASE
                - NotStatement:
                    Statement:
                      AndStatement:
                        Statements:
                          - ByteMatchStatement:
                              FieldToMatch:
                                UriPath: { }
                              PositionalConstraint: STARTS_WITH
                              SearchString: "/api/enforcements"
                              TextTransformations:
                                - Type: NONE
                                  Priority: 0
                          - ByteMatchStatement:
                              FieldToMatch:
                                Body:
                                  OversizeHandling: CONTINUE
                              PositionalConstraint: CONTAINS
                              SearchString: "\"action_name\":\"send_emails\""
                              TextTransformations:
                                - Type: NONE
                                  Priority: 0
                - NotStatement:
                    Statement:
                      RegexMatchStatement:
                        RegexString: \/api\/dashboard\/charts\/*|\/api\/reports(\/.*)?|\/api\/dashboard\/[a-z0-9]{24}\/?
                        FieldToMatch:
                          UriPath: { }
                        TextTransformations:
                          - Priority: 0
                            Type: NONE
                - NotStatement:
                    Statement:
                      AndStatement:
                        Statements:
                          - ByteMatchStatement:
                              FieldToMatch:
                                UriPath: { }
                              PositionalConstraint: EXACTLY
                              SearchString: /api/settings/plugins/gui/upload_image_file
                              TextTransformations:
                                - Priority: 0
                                  Type: NONE
                          - ByteMatchStatement:
                              SearchString: "PUT"
                              FieldToMatch:
                                Method: { }
                              TextTransformations:
                                - Priority: 0
                                  Type: NONE
                              PositionalConstraint: EXACTLY
                - NotStatement:
                    Statement:
                      AndStatement:
                        Statements:
                          - ByteMatchStatement:
                              FieldToMatch:
                                UriPath: { }
                              PositionalConstraint: EXACTLY
                              SearchString: /api/adapters/custom/upload_image_file
                              TextTransformations:
                                - Priority: 0
                                  Type: NONE
                          - ByteMatchStatement:
                              SearchString: "PUT"
                              FieldToMatch:
                                Method: { }
                              TextTransformations:
                                - Priority: 0
                                  Type: NONE
                              PositionalConstraint: EXACTLY
                - NotStatement:
                    Statement:
                      AndStatement:
                        Statements:
                          - RegexMatchStatement:
                              RegexString: \/api\/[a-zA-Z_]*\/[a-z0-9]*\/notes(?:\/[a-z0-9-]*)?$
                              FieldToMatch:
                                UriPath: {}
                              TextTransformations:
                                - Priority: 0
                                  Type: NONE
                          - ByteMatchStatement:
                              FieldToMatch:
                                Body:
                                  OversizeHandling: CONTINUE
                              PositionalConstraint: CONTAINS
                              SearchString: "style"
                              TextTransformations:
                                - Priority: 0
                                  Type: NONE
                          - OrStatement:  # Nested OrStatement here
                              Statements:
                                - ByteMatchStatement:
                                    FieldToMatch:
                                      Method: {}
                                    PositionalConstraint: EXACTLY
                                    SearchString: "POST"
                                    TextTransformations:
                                      - Type: NONE
                                        Priority: 0
                                - ByteMatchStatement:
                                    FieldToMatch:
                                      Method: {}
                                    PositionalConstraint: EXACTLY
                                    SearchString: "PUT"
                                    TextTransformations:
                                      - Type: NONE
                                        Priority: 0
        - Name: AX_Block_LFI_QueryString
          Priority: 11
          Action:
            Count: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: AXBlockLFIQueryString
          Statement:
            AndStatement:
              Statements:
                - LabelMatchStatement:
                    Key: awswaf:managed:aws:linux-os:LFI_QueryString
                    Scope: LABEL
                - NotStatement:
                    Statement:
                      RegexPatternSetReferenceStatement:
                        Arn: !GetAtt WAFRegexPatternSet.Arn
                        FieldToMatch:
                          UriPath: {}
                        TextTransformations:
                        - Priority: 0
                          Type: NONE
        - Name: AX_Block_GenericRFI_QueryArguments
          Priority: 12
          Action:
            Count: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: AXBlockGenericRFIQueryArguments
          Statement:
            AndStatement:
              Statements:
                - LabelMatchStatement:
                    Key: awswaf:managed:aws:core-rule-set:GenericRFI_QueryArguments
                    Scope: LABEL
                - NotStatement:
                    Statement:
                      RegexPatternSetReferenceStatement:
                        Arn: !GetAtt WAFRegexPatternSet.Arn
                        FieldToMatch:
                          UriPath: {}
                        TextTransformations:
                        - Priority: 0
                          Type: NONE
        - Name: AX_Block_EC2MetaDataSSRF_Cookie
          Priority: 13
          Action:
            Count: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: AXBlockEC2MetaDataSSRFCookie
          Statement:
            AndStatement:
              Statements:
                - LabelMatchStatement:
                    Key: awswaf:managed:aws:core-rule-set:EC2MetaDataSSRF_Cookie
                    Scope: LABEL
                - RegexMatchStatement:
                    RegexString: \/api\/([vV]4\.0\/)?(?:enforcements|adapters)\/.*
                    FieldToMatch:
                      UriPath: { }
                    TextTransformations:
                      - Priority: 0
                        Type: NONE
        - Name: AX_Block_EC2MetaDataSSRF_UriPath
          Priority: 14
          Action:
            Count: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: AXBlockEC2MetaDataSSRFURIPATH
          Statement:
            AndStatement:
              Statements:
                - LabelMatchStatement:
                    Key: awswaf:managed:aws:core-rule-set:EC2MetaDataSSRF_UriPath
                    Scope: LABEL
                - RegexMatchStatement:
                    RegexString: \/api\/([vV]4\.0\/)?(?:enforcements|adapters)\/.*
                    FieldToMatch:
                      UriPath: { }
                    TextTransformations:
                      - Priority: 0
                        Type: NONE
      Tags:
        - Key: CreatedBy
          Value: "cloudformation"
        - Key: Environment
          Value: !Ref Environment
        - Key: ax:business-importance
          Value: !If [ProdMachineCondition, "high", "low"]
        - Key: ax:business-owner
          Value: rnd
        - Key: ax:business-process
          Value: rnd
        - Key: ax:classification
          Value: confidential-restricted
        - Key: ax:creator
          Value: saas-control
        - Key: ax:customer-type
          Value: poc
        - Key: ax:environment
          Value: !Ref Environment
        - Key: ax:lifecycle-type
          Value: cloudformation
        - Key: ax:resource-type
          Value: permanent
        - Key: ax:system-name
          Value: cortex
        - Key: ax:system-type
          Value: customer
        - Key: ax:technical-owner
          Value: devops
      VisibilityConfig:
        SampledRequestsEnabled: true
        CloudWatchMetricsEnabled: true
        MetricName: !Sub ${AWS::Region}-WebAclMetric

  WAFWebAclLoggingConfiguration:
    Type: AWS::WAFv2::LoggingConfiguration
    Properties:
      LogDestinationConfigs:
        - !If [ProdAccount, "arn:aws:s3:us-east-1:************:aws-waf-logs-ax-prod-d57b99cfa16f", "arn:aws:s3:us-east-1:************:aws-waf-logs-ax-dev-a04a95838edc"]
      LoggingFilter:
        DefaultBehavior: DROP
        Filters:
          - Behavior: KEEP
            Conditions:
              - ActionCondition:
                  Action: COUNT
            Requirement: MEETS_ALL
          - Behavior: KEEP
            Conditions:
              - ActionCondition:
                  Action: BLOCK
            Requirement: MEETS_ALL
      RedactedFields:
        - SingleHeader:
            Name: api-secret
        - SingleHeader:
            Name: cookie
      ResourceArn: !GetAtt WAFWebAcl.Arn

  EnforcementWAFWebAcl:
    Type: AWS::WAFv2::WebACL
    Properties:
      DefaultAction:
        Allow: { }
      Description: This is a WAF WebAcl that should be used for all customers
      Name: !Sub ${AWS::Region}-WebAcl-Enforcement
      Scope: REGIONAL
      Rules:
        - Name: AmazonIpReputationList
          Priority: 0
          OverrideAction:
            None: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: EnforcementAmazonIpReputationListMetric
          Statement:
            ManagedRuleGroupStatement:
              VendorName: AWS
              Name: AWSManagedRulesAmazonIpReputationList
              ExcludedRules: [ ]
        - Name: AX_AmazonIpReputationList_Block405
          Priority: 1
          Action:
            Block:
              CustomResponse:
                ResponseCode: 405
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: EnforcementAmazonIpReputationListBlock405Metric
          Statement:
            LabelMatchStatement:
              Scope: NAMESPACE
              Key: "awswaf:managed:aws:amazon-ip-list:"
        - Name: AnonymousIpList
          Priority: 2
          OverrideAction:
            None: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: EnforcementAnonymousIpListMetric
          Statement:
            ManagedRuleGroupStatement:
              VendorName: AWS
              Name: AWSManagedRulesAnonymousIpList
              RuleActionOverrides:
                # Requested not to inspect as it might block IP coming from hosting providers
                - ActionToUse:
                    Count: { }
                  Name: HostingProviderIPList
              ExcludedRules: [ ]
        - Name: RateLimit
          Priority: 3
          Action:
            Count: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: EnforcementRateLimitMetric
          Statement:
            RateBasedStatement:
              Limit: 5000
              AggregateKeyType: IP
        - Name: CommonRuleSet
          Priority: 4
          OverrideAction:
            None: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: EnforcementCommonRuleSetMetric
          Statement:
            ManagedRuleGroupStatement:
              VendorName: AWS
              Name: AWSManagedRulesCommonRuleSet
              Version: Version_1.6
              RuleActionOverrides:
              # Override default block action to skip to the next rule in order to get 405.
                - ActionToUse:
                    Count: { }
                  Name: CrossSiteScripting_URIPATH
                - ActionToUse:
                    Count: { }
                  Name: CrossSiteScripting_COOKIE
                - ActionToUse:
                    Count: { }
                  Name: CrossSiteScripting_QUERYARGUMENTS
                - ActionToUse:
                    Count: { }
                  Name: GenericRFI_URIPATH
                - ActionToUse:
                    Count: { }
                  Name: RestrictedExtensions_QUERYARGUMENTS
                - ActionToUse:
                    Count: { }
                  Name: RestrictedExtensions_URIPATH
                - ActionToUse:
                    Count: { }
                  Name: GenericLFI_URIPATH
                - ActionToUse:
                    Count: { }
                  Name: GenericLFI_QUERYARGUMENTS
                - ActionToUse:
                    Count: { }
                  Name: SizeRestrictions_URIPATH
                - ActionToUse:
                    Count: { }
                  Name: SizeRestrictions_Cookie_HEADER
                - ActionToUse:
                    Count: { }
                  Name: UserAgent_BadBots_HEADER
                # Requested not to inspect as it might block machine <> machine traffic
                - ActionToUse:
                    Count: { }
                  Name: NoUserAgent_HEADER
                # Count should not be modified as might impact legit traffic
                - ActionToUse:
                    Count: { }
                  Name: SizeRestrictions_BODY
                # Count should not be modified as might impact legit traffic
                - ActionToUse:
                    Count: { }
                  Name: SizeRestrictions_QUERYSTRING
                # Count action should not be changed as we filter and block by AX_Block_EC2MetaDataSSRF_Body rule
                - ActionToUse:
                    Count: { }
                  Name: EC2MetaDataSSRF_BODY
                # Count action should not be changed as we filter and block by AX_Block_GenericRFI_Body rule
                - ActionToUse:
                    Count: { }
                  Name: GenericRFI_BODY
                  # Count action should not be changed as we filter and block by AX_Block_GenericLFI_Body rule
                - ActionToUse:
                    Count: { }
                  Name: GenericLFI_BODY
                # Count action should not be changed as we filter and block by AX_Block_EC2MetaDataSSRF_QueryArguments rule
                - ActionToUse:
                    Count: { }
                  Name: EC2MetaDataSSRF_QUERYARGUMENTS
                # Count action should not be changed as we filter and block by AX_Block_CrossSiteScripting_Body rule
                - ActionToUse:
                    Count: { }
                  Name: CrossSiteScripting_BODY
                # Count action should not be changed as we filter and block by AX_Block_GenericRFI_QueryArguments rule
                - ActionToUse:
                    Count: { }
                  Name: GenericRFI_QUERYARGUMENTS
                # Count action should not be changed as we filter and block by AX_Block_GenericRFI_Cookie rule
                - ActionToUse:
                    Count: { }
                  Name: EC2MetaDataSSRF_COOKIE
                # Count action should not be changed as we filter and block by AX_Block_GenericRFI_UriPath rule
                - ActionToUse:
                    Count: { }
                  Name: EC2MetaDataSSRF_URIPATH
              ExcludedRules: [ ]
        - Name: AX_CommonRuleSet_Block405
          Priority: 5
          Action:
            Block:
              CustomResponse:
                ResponseCode: 405
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: EnforcementCommonRuleSetMetricBlock405Metric
          Statement:
             OrStatement:
               Statements:
                 - LabelMatchStatement:
                     Scope: LABEL
                     Key: "awswaf:managed:aws:core-rule-set:GenericRFI_URIPath"
                 - LabelMatchStatement:
                     Scope: LABEL
                     Key: "awswaf:managed:aws:core-rule-set:RestrictedExtensions_QueryArguments"
                 - LabelMatchStatement:
                     Scope: LABEL
                     Key: "awswaf:managed:aws:core-rule-set:CrossSiteScripting_URIPath"
                 - LabelMatchStatement:
                     Scope: LABEL
                     Key: "awswaf:managed:aws:core-rule-set:CrossSiteScripting_QueryArguments"
                 - LabelMatchStatement:
                     Scope: LABEL
                     Key: "awswaf:managed:aws:core-rule-set:CrossSiteScripting_Cookie"
                 - LabelMatchStatement:
                     Scope: LABEL
                     Key: "awswaf:managed:aws:core-rule-set:RestrictedExtensions_URIPath"
                 - LabelMatchStatement:
                     Scope: LABEL
                     Key: "awswaf:managed:aws:core-rule-set:GenericLFI_URIPath"
                 - LabelMatchStatement:
                     Scope: LABEL
                     Key: "awswaf:managed:aws:core-rule-set:GenericLFI_QueryArguments"
                 - LabelMatchStatement:
                     Scope: LABEL
                     Key: "awswaf:managed:aws:core-rule-set:SizeRestrictions_URIPath"
                 - LabelMatchStatement:
                     Scope: LABEL
                     Key: "awswaf:managed:aws:core-rule-set:SizeRestrictions_Cookie_Header"
                 - LabelMatchStatement:
                     Scope: LABEL
                     Key: "awswaf:managed:aws:core-rule-set:BadBots_Header"
        - Name: KnownBadInputsRuleSet
          Priority: 6
          OverrideAction:
            Count: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: EnforcementKnownBadInputsRuleSetMetric
          Statement:
            ManagedRuleGroupStatement:
              VendorName: AWS
              Name: AWSManagedRulesKnownBadInputsRuleSet
              Version: Version_1.16
              RuleActionOverrides:
               # Override default block action to skip to the next rule in order to get 405.
                - ActionToUse:
                    Count: { }
                  Name: Host_localhost_HEADER
                - ActionToUse:
                    Count: { }
                  Name: PROPFIND_METHOD
                - ActionToUse:
                    Count: { }
                  Name: ExploitablePaths_URIPATH
                - ActionToUse: # Requested not to inspect
                    Count: { }
                  Name: JavaDeserializationRCE_BODY
                - ActionToUse: # Requested not to inspect
                    Count: { }
                  Name: JavaDeserializationRCE_URIPATH
                - ActionToUse: # Requested not to inspect
                    Count: { }
                  Name: JavaDeserializationRCE_QUERYSTRING
                - ActionToUse: # Requested not to inspect
                    Count: { }
                  Name: JavaDeserializationRCE_HEADER
                - ActionToUse: # Requested not to inspect
                    Count: { }
                  Name: Log4JRCE_QUERYSTRING
                - ActionToUse: # Requested not to inspect
                    Count: { }
                  Name: Log4JRCE_BODY
                - ActionToUse: # Requested not to inspect
                    Count: { }
                  Name: Log4JRCE_HEADER
                - ActionToUse: # Requested not to inspect
                    Count: { }
                  Name: Log4JRCE_URIPATH
              ExcludedRules: [ ]
        - Name: AX_KnownBadInputsRuleSet_Block405
          Priority: 7
          Action:
            Block:
              CustomResponse:
                ResponseCode: 405
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: EnforcementKnownBadInputsRuleSetMetricBlock405Metric
          Statement:
            OrStatement:
              Statements:
                - LabelMatchStatement:
                    Scope: LABEL
                    Key: "awswaf:managed:aws:known-bad-inputs:Host_Localhost_Header"
                - LabelMatchStatement:
                    Scope: LABEL
                    Key: "awswaf:managed:aws:known-bad-inputs:Propfind_Method"
                - LabelMatchStatement:
                    Scope: LABEL
                    Key: "awswaf:managed:aws:known-bad-inputs:ExploitablePaths_URIPath"
        - Name: RulesLinuxRuleSet
          Priority: 8
          OverrideAction:
            Count: { }
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: EnforcementRulesLinuxRuleSetMetric
          Statement:
            ManagedRuleGroupStatement:
              VendorName: AWS
              Name: AWSManagedRulesLinuxRuleSet
              Version: !If [AWSRegionsAfter2022, "Version_2.1", "Version_2.2"]
              RuleActionOverrides:
                 # Override default block action to skip to the next rule in order to get 405.
                - ActionToUse:
                    Count: { }
                  Name: LFI_URIPATH
                - ActionToUse:
                    Count: { }
                  Name: LFI_HEADER
                - ActionToUse: # Count action should not be changed as we filter and block by AX_Block_LFI_QueryString rule
                    Count: { }
                  Name: LFI_QUERYSTRING
              ExcludedRules: [ ]
        - Name: AX_RulesLinuxRuleSet_Block405
          Priority: 9
          Action:
            Block:
              CustomResponse:
                ResponseCode: 405
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: EnforcementRulesLinuxRuleSetMetricBlock405Metric
          Statement:
            OrStatement:
              Statements:
                - LabelMatchStatement:
                    Scope: LABEL
                    Key: "awswaf:managed:aws:linux-os:LFI_URIPath"
                - LabelMatchStatement:
                    Scope: LABEL
                    Key: "awswaf:managed:aws:linux-os:LFI_Header"
        - Name: AX_Block_GenericRFI_Body
          Priority: 10
          Action:
            Block:
              CustomResponse:
                ResponseCode: 405
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: AXBlockGenericRFIBody
          Statement:
            AndStatement:
              Statements:
                - LabelMatchStatement:
                    Key: awswaf:managed:aws:core-rule-set:GenericRFI_Body
                    Scope: LABEL
                - NotStatement:
                    Statement:
                      RegexPatternSetReferenceStatement:
                        Arn: !GetAtt WAFRegexPatternSet.Arn
                        FieldToMatch:
                          UriPath: {}
                        TextTransformations:
                        - Priority: 0
                          Type: NONE
        - Name: AX_Block_GenericLFI_Body
          Priority: 11
          Action:
            Block:
               CustomResponse:
                 ResponseCode: 405
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: AXBlockGenericLFIBody
          Statement:
            AndStatement:
              Statements:
                - LabelMatchStatement:
                    Key: awswaf:managed:aws:core-rule-set:GenericLFI_Body
                    Scope: LABEL
                - NotStatement:
                    Statement:
                      AndStatement:
                        Statements:
                          - RegexMatchStatement:
                              RegexString: \/api\/([vV]4\.0\/)?adapters/nmap_adapter/upload_file$
                              FieldToMatch:
                                UriPath: { }
                              TextTransformations:
                                - Priority: 0
                                  Type: NONE
                          - ByteMatchStatement:
                              FieldToMatch:
                                Body:
                                  OversizeHandling: CONTINUE
                              PositionalConstraint: CONTAINS
                              SearchString: 'content-type: text/xml'
                              TextTransformations:
                                - Priority: 0
                                  Type: LOWERCASE
                          - ByteMatchStatement:
                              FieldToMatch:
                                Body:
                                  OversizeHandling: CONTINUE
                              PositionalConstraint: CONTAINS
                              SearchString: '<?xml'
                              TextTransformations:
                                - Priority: 0
                                  Type: LOWERCASE
        - Name: AX_Block_EC2MetaDataSSRF_Body
          Priority: 12
          Action:
            Block:
              CustomResponse:
                ResponseCode: 405
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: AXBlockEC2MetaDataSSRFBody
          Statement:
            AndStatement:
              Statements:
                - LabelMatchStatement:
                    Key: awswaf:managed:aws:core-rule-set:EC2MetaDataSSRF_Body
                    Scope: LABEL
                - RegexMatchStatement:
                    RegexString: \/api\/([vV]4\.0\/)?(?:enforcements|adapters)\/.*
                    FieldToMatch:
                      UriPath: {}
                    TextTransformations:
                    - Priority: 0
                      Type: NONE
                - NotStatement:
                    Statement:
                      RegexPatternSetReferenceStatement:
                        Arn: !GetAtt WAFRegexPatternSetAdapters.Arn
                        FieldToMatch:
                          UriPath: { }
                        TextTransformations:
                          - Priority: 0
                            Type: NONE
                - NotStatement:
                    Statement:
                      ByteMatchStatement:
                        FieldToMatch:
                          Body:
                            OversizeHandling: CONTINUE
                        PositionalConstraint: CONTAINS
                        SearchString: '"action":{"action_name":"send_data_to_ms_power_bi"'
                        TextTransformations:
                          - Priority: 0
                            Type: LOWERCASE
        - Name: AX_Block_EC2MetaDataSSRF_QueryArguments
          Priority: 13
          Action:
            Block:
              CustomResponse:
                ResponseCode: 405
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: AXBlockEC2MetaDataSSRFQueryArguments
          Statement:
            AndStatement:
              Statements:
                - LabelMatchStatement:
                    Key: awswaf:managed:aws:core-rule-set:EC2MetaDataSSRF_QueryArguments
                    Scope: LABEL
                - RegexMatchStatement:
                    RegexString: \/api\/([vV]4\.0\/)?(?:enforcements|adapters)\/.*
                    FieldToMatch:
                      UriPath: {}
                    TextTransformations:
                    - Priority: 0
                      Type: NONE
                - NotStatement:
                    Statement:
                      RegexPatternSetReferenceStatement:
                        Arn: !GetAtt WAFRegexPatternSetAdapters.Arn
                        FieldToMatch:
                          UriPath: { }
                        TextTransformations:
                          - Priority: 0
                            Type: NONE
        - Name: AX_Block_CrossSiteScripting_Body
          Priority: 14
          Action:
            Block:
              CustomResponse:
                ResponseCode: 405
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: AXBlockCrossSiteScriptingBody
          Statement:
            AndStatement:
              Statements:
                - LabelMatchStatement:
                    Key: awswaf:managed:aws:core-rule-set:CrossSiteScripting_Body
                    Scope: LABEL
                - NotStatement:
                    Statement:
                      AndStatement:
                        Statements:
                        - RegexMatchStatement:
                            RegexString: \/api\/([vV]4\.0\/)?adapters\/nmap_adapter\/upload_file$
                            FieldToMatch:
                              UriPath: { }
                            TextTransformations:
                              - Priority: 0
                                Type: NONE
                        - ByteMatchStatement:
                            FieldToMatch:
                              Body:
                                OversizeHandling: CONTINUE
                            PositionalConstraint: CONTAINS
                            SearchString: 'content-type: text/xml'
                            TextTransformations:
                              - Priority: 0
                                Type: LOWERCASE
                        - ByteMatchStatement:
                            FieldToMatch:
                              Body:
                                OversizeHandling: CONTINUE
                            PositionalConstraint: CONTAINS
                            SearchString: '<?xml'
                            TextTransformations:
                              - Priority: 0
                                Type: LOWERCASE
                - NotStatement:
                    Statement:
                      AndStatement:
                        Statements:
                          - ByteMatchStatement:
                              FieldToMatch:
                                UriPath: { }
                              PositionalConstraint: EXACTLY
                              SearchString: /api/enforcements/actions/upload_file
                              TextTransformations:
                                - Priority: 0
                                  Type: NONE
                          - ByteMatchStatement:
                              FieldToMatch:
                                Body:
                                  OversizeHandling: CONTINUE
                              PositionalConstraint: CONTAINS
                              SearchString: 'content-type: application/octet-stream'
                              TextTransformations:
                                - Priority: 0
                                  Type: LOWERCASE
                - NotStatement:
                    Statement:
                      AndStatement:
                        Statements:
                        - ByteMatchStatement:
                            FieldToMatch:
                              UriPath: {}
                            PositionalConstraint: STARTS_WITH
                            SearchString: "/api/enforcements"
                            TextTransformations:
                            - Type: NONE
                              Priority: 0
                        - ByteMatchStatement:
                            FieldToMatch:
                              Body:
                                OversizeHandling: CONTINUE
                            PositionalConstraint: CONTAINS
                            SearchString: "\"action_name\":\"send_emails\""
                            TextTransformations:
                            - Type: NONE
                              Priority: 0
                - NotStatement:
                    Statement:
                      AndStatement:
                        Statements:
                        - ByteMatchStatement:
                            FieldToMatch:
                              UriPath: {}
                            PositionalConstraint: STARTS_WITH
                            SearchString: "/api/enforcements"
                            TextTransformations:
                            - Type: NONE
                              Priority: 0
                        - ByteMatchStatement:
                            FieldToMatch:
                              Body:
                                OversizeHandling: CONTINUE
                            PositionalConstraint: CONTAINS
                            SearchString: "\"action_name\":\"send_email_to_entities\""
                            TextTransformations:
                            - Type: NONE
                              Priority: 0
                - NotStatement:
                    Statement:
                      RegexMatchStatement:
                        RegexString: \/api\/dashboard\/charts\/*|\/api\/reports(\/.*)?|\/api\/dashboard\/[a-z0-9]{24}\/?
                        FieldToMatch:
                          UriPath: { }
                        TextTransformations:
                          - Priority: 0
                            Type: NONE
                - NotStatement:
                    Statement:
                      AndStatement:
                        Statements:
                          - ByteMatchStatement:
                              FieldToMatch:
                                UriPath: { }
                              PositionalConstraint: EXACTLY
                              SearchString: /api/settings/plugins/gui/upload_image_file
                              TextTransformations:
                                - Priority: 0
                                  Type: NONE
                          - ByteMatchStatement:
                              SearchString: "PUT"
                              FieldToMatch:
                                Method: { }
                              TextTransformations:
                                - Priority: 0
                                  Type: NONE
                              PositionalConstraint: EXACTLY
                - NotStatement:
                    Statement:
                      AndStatement:
                        Statements:
                          - ByteMatchStatement:
                              FieldToMatch:
                                UriPath: { }
                              PositionalConstraint: EXACTLY
                              SearchString: /api/adapters/custom/upload_image_file
                              TextTransformations:
                                - Priority: 0
                                  Type: NONE
                          - ByteMatchStatement:
                              SearchString: "PUT"
                              FieldToMatch:
                                Method: { }
                              TextTransformations:
                                - Priority: 0
                                  Type: NONE
                              PositionalConstraint: EXACTLY
                - NotStatement:
                    Statement:
                      AndStatement:
                        Statements:
                          - RegexMatchStatement:
                              RegexString: \/api\/[a-zA-Z_]*\/[a-z0-9]*\/notes(?:\/[a-z0-9-]*)?$
                              FieldToMatch:
                                UriPath: {}
                              TextTransformations:
                                - Priority: 0
                                  Type: NONE
                          - ByteMatchStatement:
                              FieldToMatch:
                                Body:
                                  OversizeHandling: CONTINUE
                              PositionalConstraint: CONTAINS
                              SearchString: "style"
                              TextTransformations:
                                - Priority: 0
                                  Type: NONE
                          - OrStatement:  # Nested OrStatement here
                              Statements:
                                - ByteMatchStatement:
                                    FieldToMatch:
                                      Method: {}
                                    PositionalConstraint: EXACTLY
                                    SearchString: "POST"
                                    TextTransformations:
                                      - Type: NONE
                                        Priority: 0
                                - ByteMatchStatement:
                                    FieldToMatch:
                                      Method: {}
                                    PositionalConstraint: EXACTLY
                                    SearchString: "PUT"
                                    TextTransformations:
                                      - Type: NONE
                                        Priority: 0
        - Name: AX_Block_LFI_QueryString
          Priority: 15
          Action:
            Block:
              CustomResponse:
                ResponseCode: 405
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: AXBlockLFIQueryString
          Statement:
            AndStatement:
              Statements:
                - LabelMatchStatement:
                    Key: awswaf:managed:aws:linux-os:LFI_QueryString
                    Scope: LABEL
                - NotStatement:
                    Statement:
                      RegexPatternSetReferenceStatement:
                        Arn: !GetAtt WAFRegexPatternSet.Arn
                        FieldToMatch:
                          UriPath: {}
                        TextTransformations:
                        - Priority: 0
                          Type: NONE
        - Name: AX_Block_GenericRFI_QueryArguments
          Priority: 16
          Action:
            Block:
              CustomResponse:
                ResponseCode: 405
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: AXBlockGenericRFIQueryArguments
          Statement:
            AndStatement:
              Statements:
                - LabelMatchStatement:
                    Key: awswaf:managed:aws:core-rule-set:GenericRFI_QueryArguments
                    Scope: LABEL
                - NotStatement:
                    Statement:
                      RegexPatternSetReferenceStatement:
                        Arn: !GetAtt WAFRegexPatternSet.Arn
                        FieldToMatch:
                          UriPath: {}
                        TextTransformations:
                        - Priority: 0
                          Type: NONE
        - Name: AX_Block_EC2MetaDataSSRF_Cookie
          Priority: 17
          Action:
            Block:
              CustomResponse:
                ResponseCode: 405
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: AXBlockEC2MetaDataSSRFCookie
          Statement:
            AndStatement:
              Statements:
                - LabelMatchStatement:
                    Key: awswaf:managed:aws:core-rule-set:EC2MetaDataSSRF_Cookie
                    Scope: LABEL
                - RegexMatchStatement:
                    RegexString: \/api\/([vV]4\.0\/)?(?:enforcements|adapters)\/.*
                    FieldToMatch:
                      UriPath: { }
                    TextTransformations:
                      - Priority: 0
                        Type: NONE
        - Name: AX_Block_EC2MetaDataSSRF_UriPath
          Priority: 18
          Action:
            Block:
              CustomResponse:
                ResponseCode: 405
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: AXBlockEC2MetaDataSSRFURIPATH
          Statement:
            AndStatement:
              Statements:
                - LabelMatchStatement:
                    Key: awswaf:managed:aws:core-rule-set:EC2MetaDataSSRF_UriPath
                    Scope: LABEL
                - RegexMatchStatement:
                    RegexString: \/api\/([vV]4\.0\/)?(?:enforcements|adapters)\/.*
                    FieldToMatch:
                      UriPath: { }
                    TextTransformations:
                      - Priority: 0
                        Type: NONE
      Tags:
        - Key: CreatedBy
          Value: "cloudformation"
        - Key: Environment
          Value: !Ref Environment
        - Key: ax:business-importance
          Value: !If [ProdMachineCondition, "high", "low"]
        - Key: ax:business-owner
          Value: rnd
        - Key: ax:business-process
          Value: rnd
        - Key: ax:classification
          Value: confidential-restricted
        - Key: ax:creator
          Value: saas-control
        - Key: ax:customer-type
          Value: poc
        - Key: ax:environment
          Value: !Ref Environment
        - Key: ax:lifecycle-type
          Value: cloudformation
        - Key: ax:resource-type
          Value: permanent
        - Key: ax:system-name
          Value: cortex
        - Key: ax:system-type
          Value: customer
        - Key: ax:technical-owner
          Value: devops
      VisibilityConfig:
        SampledRequestsEnabled: true
        CloudWatchMetricsEnabled: true
        MetricName: !Sub ${AWS::Region}-WebAclMetric-Enforcement

  EnforcementWAFWebAclLoggingConfiguration:
    Type: AWS::WAFv2::LoggingConfiguration
    Properties:
      LogDestinationConfigs:
        - !If [ ProdAccount, "arn:aws:s3:us-east-1:************:aws-waf-logs-ax-prod-d57b99cfa16f", "arn:aws:s3:us-east-1:************:aws-waf-logs-ax-dev-a04a95838edc" ]
      LoggingFilter:
        DefaultBehavior: DROP
        Filters:
          - Behavior: KEEP
            Conditions:
              - ActionCondition:
                  Action: COUNT
            Requirement: MEETS_ALL
          - Behavior: KEEP
            Conditions:
              - ActionCondition:
                  Action: BLOCK
            Requirement: MEETS_ALL
      RedactedFields:
        - SingleHeader:
            Name: api-secret
        - SingleHeader:
            Name: cookie
      ResourceArn: !GetAtt EnforcementWAFWebAcl.Arn

Outputs:
  OnAxoniusCertificateArn:
    Description: "The On Axonius certificate ARN"
    Value: !Ref OnAxoniusCertificate
    Export:
      Name: !Sub "${AWS::StackName}-OnAxoniusCertificateArn"

  SaasParamKmsArn:
    Description: "The SaaS param KMS ARN"
    Value: !GetAtt SaasParamKms.Arn
    Export:
      Name: !Sub "${AWS::StackName}-SaasParamKmsArn"

  SsmCommandRegisterCustomer: # DO NOT MODIFY THE OUTPUT NAME. It is read in other places and in code
    Description: "SSM command name"
    Value: !Ref SsmCommandRegisterCustomer
    Export:
      Name: !Sub "${AWS::StackName}-SsmCommandRegisterCustomer"

  S3AccessLogArn:
    Description: "The ELB Access log S3 Bucket Name"
    Value: !Ref ELBAccessLogBucket
    Export:
      Name: !Sub "${AWS::StackName}-S3AccessLogsBucketName"

  S3ServerAccessLogArn:
    Description: "S3 server access logging target bucket"
    Value: !Ref S3ServerAccessLoggingBucket
    Export:
      Name: !Sub "${AWS::StackName}-S3ServerAccessLoggingBucketName"

  WAFWebAclArn:
    Description: "WAF WebAcl ARN"
    Value: !GetAtt WAFWebAcl.Arn
    Export:
      Name: !Sub "${AWS::StackName}-WAFWebAclArn"

  EnforcementWAFWebAclArn:
    Description: "Enforcement WAF WebAcl ARN"
    Value: !GetAtt EnforcementWAFWebAcl.Arn
    Export:
      Name: !Sub "${AWS::StackName}-WAFWebAclArn-Enforcement"